import { useState, useCallback } from "react";
import { AddonLineItem } from "../../components/supplier-management/addon-line-item";

interface AddonPricingRequest {
  addon_ids: string[];
  supplier_id?: string;
  activity_start_date?: string;
  activity_end_date?: string;
}

interface AddonPricingResponse {
  addon_id: string;
  name: string;
  supplier_name?: string;
  supplier_id?: string;

  // Complete supplier offering pricing data
  gross_price?: number;
  commission?: number;
  net_cost?: number;
  margin_rate?: number;
  selling_price?: number;
  currency?: string;
  selling_currency?: string;
  exchange_rate?: number;
  selling_price_selling_currency?: number;

  found: boolean;
  error?: string;
}

interface UseAddonPricingReturn {
  fetchAddonPricing: (request: AddonPricingRequest) => Promise<AddonLineItem[]>;
  isLoading: boolean;
  error: string | null;
}

export const useAddonPricing = (): UseAddonPricingReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateId = () => {
    return `addon_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  };

  const fetchAddonPricing = useCallback(
    async (request: AddonPricingRequest): Promise<AddonLineItem[]> => {
      console.log("🔍 Fetching addon pricing for:", request);
      setIsLoading(true);
      setError(null);

      try {
        // Build query parameters
        const params = new URLSearchParams();
        request.addon_ids.forEach((id) => params.append("addon_ids[]", id));

        if (request.supplier_id) {
          params.append("supplier_id", request.supplier_id);
        }
        if (request.activity_start_date) {
          params.append("activity_start_date", request.activity_start_date);
        }
        if (request.activity_end_date) {
          params.append("activity_end_date", request.activity_end_date);
        }

        const url = `/admin/supplier-management/addon-pricing?${params.toString()}`;
        console.log("🌐 Fetching URL:", url);

        const response = await fetch(url, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error("❌ API Error Response:", errorText);
          throw new Error(
            `Failed to fetch addon pricing: ${response.statusText}`
          );
        }

        const data = await response.json();
        console.log("📦 API Response:", data);
        const addonPricingResults: AddonPricingResponse[] =
          data.addon_pricing || [];

        // Convert API response to AddonLineItem format
        const lineItems: AddonLineItem[] = addonPricingResults.map(
          (result) => ({
            id: generateId(),
            addon_id: result.addon_id,
            name: result.name,
            supplier_name: result.supplier_name,

            // Complete supplier offering pricing data
            gross_price: result.gross_price,
            commission: result.commission,
            net_cost: result.net_cost,
            margin_rate: result.margin_rate,
            selling_price: result.selling_price,
            currency: result.currency,
            selling_currency: result.selling_currency,
            exchange_rate: result.exchange_rate,
            selling_price_selling_currency:
              result.selling_price_selling_currency,

            // Addon requirement configuration
            is_mandatory: false, // Default to optional, user can toggle to mandatory

            is_auto_populated: true,
            is_manual: false,
          })
        );

        console.log("🔄 Converted to line items:", lineItems);
        return lineItems;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error occurred";
        setError(errorMessage);
        console.error("Error fetching addon pricing:", err);

        // Return empty line items with names only (no prices) as fallback
        // This requires fetching product service names separately
        return [];
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  return {
    fetchAddonPricing,
    isLoading,
    error,
  };
};

// Helper hook to create addon line items from selection (with fallback for missing prices)
export const useAddonLineItemsFromSelection = () => {
  const { fetchAddonPricing, isLoading, error } = useAddonPricing();

  const createLineItemsFromSelection = useCallback(
    async (
      selectedAddonIds: string[],
      supplierOffering?: {
        supplier_id?: string;
        activity_start_date?: string;
        activity_end_date?: string;
      },
      productServicesData?: Array<{ id: string; name: string }>
    ): Promise<AddonLineItem[]> => {
      if (selectedAddonIds.length === 0) {
        return [];
      }

      try {
        // Try to fetch pricing data
        const lineItems = await fetchAddonPricing({
          addon_ids: selectedAddonIds,
          supplier_id: supplierOffering?.supplier_id,
          activity_start_date: supplierOffering?.activity_start_date,
          activity_end_date: supplierOffering?.activity_end_date,
        });

        // If we got results, return them
        if (lineItems.length > 0) {
          return lineItems;
        }

        // Fallback: create line items without prices using product services data
        if (productServicesData) {
          return selectedAddonIds.map((addonId) => {
            const productService = productServicesData.find(
              (ps) => ps.id === addonId
            );
            return {
              id: `addon_${Date.now()}_${Math.random()
                .toString(36)
                .substring(2, 11)}`,
              addon_id: addonId,
              name: productService?.name || `Unknown Addon (${addonId})`,
              supplier_name: undefined,
              price: undefined,
              is_auto_populated: true,
              is_manual: false,
            };
          });
        }

        // Last resort: create line items with addon IDs as names
        return selectedAddonIds.map((addonId) => ({
          id: `addon_${Date.now()}_${Math.random()
            .toString(36)
            .substring(2, 11)}`,
          addon_id: addonId,
          name: `Addon ${addonId}`,
          supplier_name: undefined,
          price: undefined,
          is_auto_populated: true,
          is_manual: false,
        }));
      } catch (err) {
        console.error("Error creating line items from selection:", err);

        // Fallback: create basic line items
        return selectedAddonIds.map((addonId) => ({
          id: `addon_${Date.now()}_${Math.random()
            .toString(36)
            .substring(2, 11)}`,
          addon_id: addonId,
          name: `Addon ${addonId}`,
          supplier_name: undefined,
          price: undefined,
          is_auto_populated: true,
          is_manual: false,
        }));
      }
    },
    [fetchAddonPricing]
  );

  return {
    createLineItemsFromSelection,
    isLoading,
    error,
  };
};
