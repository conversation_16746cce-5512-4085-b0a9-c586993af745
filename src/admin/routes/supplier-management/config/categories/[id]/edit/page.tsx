import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft } from "@camped-ai/icons";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Textarea,
  Select,
  Label,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../../../widgets/permission-based-sidebar-hider";
import {
  useCategory,
  useUpdateCategory,
  useCategoryUsageCheck,
} from "../../../../../../hooks/supplier-products-services/use-categories";
import DynamicFieldSchemaBuilder, {
  type DynamicFieldSchema,
} from "../../../../../../components/supplier-management/dynamic-field-schema-builder";

interface FormData {
  name: string;
  description: string;
  category_type: "Product" | "Service" | "Both";
  icon: string;
  dynamic_field_schema: DynamicFieldSchema[];
  is_active: boolean;
}

const EditCategoryPage = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const updateCategory = useUpdateCategory();
  const categoryUsageCheck = useCategoryUsageCheck();

  // Fetch category data
  const {
    data: categoryData,
    isLoading: categoryLoading,
    error: categoryError,
  } = useCategory(id!);
  const category = categoryData?.category;

  const [formData, setFormData] = useState<FormData>({
    name: "",
    description: "",
    category_type: "Both",
    icon: "",
    dynamic_field_schema: [],
    is_active: true,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [initialized, setInitialized] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Initialize form data when category is loaded
  useEffect(() => {
    if (category && !initialized) {
      setFormData({
        name: category.name,
        description: category.description || "",
        category_type: category.category_type || "Both",
        icon: category.icon || "",
        dynamic_field_schema: category.dynamic_field_schema || [],
        is_active: category.is_active,
      });
      setInitialized(true);
    }
  }, [category, initialized]);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "Name must be at least 2 characters long";
    }

    // Validate dynamic field schema
    if (formData.dynamic_field_schema.length > 0) {
      const fieldKeys = new Set();
      for (let i = 0; i < formData.dynamic_field_schema.length; i++) {
        const field = formData.dynamic_field_schema[i];

        if (!field.label.trim()) {
          newErrors.dynamic_field_schema = "All fields must have a label";
          break;
        }

        if (!field.key.trim()) {
          newErrors.dynamic_field_schema = "All fields must have a key";
          break;
        }

        if (fieldKeys.has(field.key)) {
          newErrors.dynamic_field_schema = "Field keys must be unique";
          break;
        }

        fieldKeys.add(field.key);

        if (
          (field.type === "dropdown" || field.type === "multi-select") &&
          (!field.options || field.options.length === 0)
        ) {
          newErrors.dynamic_field_schema =
            "Dropdown and multi-select fields must have options";
          break;
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Please fill all the required fields");
      return;
    }

    if (!id) {
      toast.error("Category ID is missing");
      return;
    }

    setLoading(true);
    try {
      // Check if we're trying to inactivate the category
      if (category?.is_active && !formData.is_active) {
        // Check usage before allowing inactivation
        const usageResult = await categoryUsageCheck.mutateAsync(id);
        const { canInactivate, usage } = usageResult;

        if (!canInactivate) {
          const errorMessage = `Cannot inactivate "${formData.name}" because it has ${usage.activeCount} active item(s) (${usage.activeProductCount} product(s), ${usage.activeServiceCount} service(s)). Please inactivate these items first before inactivating the category.`;
          toast.error(errorMessage);
          setLoading(false);
          return;
        }
      }

      const data = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        category_type: formData.category_type,
        icon: formData.icon.trim() || undefined,
        dynamic_field_schema: formData.dynamic_field_schema as any,
        is_active: formData.is_active,
      };

      await updateCategory.mutateAsync({ id, data });

      // Reset unsaved changes flag
      setHasUnsavedChanges(false);

      // Don't show toast here as it's handled by the mutation hook
      navigate("/supplier-management/config/categories");
    } catch (error) {
      console.error("Error updating category:", error);

      // Extract meaningful error message
      let errorMessage = "Failed to update category. Please try again.";

      if (error instanceof Error) {
        if (error.message.includes("already exists")) {
          errorMessage = "A category with this name already exists";
        } else if (error.message.includes("not found")) {
          errorMessage = "Category not found";
        } else {
          errorMessage = error.message;
        }
      }

      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      if (
        window.confirm(
          "You have unsaved changes. Are you sure you want to leave without saving?"
        )
      ) {
        navigate("/supplier-management/config/categories");
      }
    } else {
      navigate("/supplier-management/config/categories");
    }
  };

  // Handle loading and error states
  if (categoryLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="divide-y p-0">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-center gap-x-4">
              <Button variant="transparent" onClick={handleCancel}>
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <Heading level="h2">Edit Category</Heading>
                <Text className="text-ui-fg-subtle">Loading...</Text>
              </div>
            </div>
          </div>
          <div className="px-6 py-8">
            <div className="animate-pulse space-y-8">
              {/* Basic Information Section */}
              <div className="space-y-4">
                <div className="h-6 bg-gray-200 rounded w-1/4"></div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                    <div className="h-10 bg-gray-200 rounded"></div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                    <div className="h-10 bg-gray-200 rounded"></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-20 bg-gray-200 rounded"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                  <div className="h-10 bg-gray-200 rounded"></div>
                </div>
              </div>

              {/* Dynamic Field Configuration Section */}
              <div className="space-y-4">
                <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                <div className="h-32 bg-gray-200 rounded"></div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-4 pt-6 border-t">
                <div className="h-10 bg-gray-200 rounded w-32"></div>
                <div className="h-10 bg-gray-200 rounded w-24"></div>
              </div>
            </div>
          </div>
        </Container>
      </>
    );
  }

  if (categoryError || !category) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="divide-y p-0">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-center gap-x-4">
              <Button
                variant="transparent"
                onClick={() =>
                  navigate("/supplier-management/config/categories")
                }
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <Heading level="h2">Edit Category</Heading>
                <Text className="text-ui-fg-subtle">Category not found</Text>
              </div>
            </div>
          </div>
          <div className="px-6 py-8">
            <div className="text-center space-y-4">
              <div className="text-red-600 text-lg font-medium">
                {categoryError?.message?.includes("not found")
                  ? "Category Not Found"
                  : "Failed to Load Category"}
              </div>
              <Text className="text-gray-600">
                {categoryError?.message?.includes("not found")
                  ? "The category you're looking for doesn't exist or may have been deleted."
                  : "There was an error loading the category. Please check your connection and try again."}
              </Text>
              <div className="flex justify-center gap-3">
                <Button
                  variant="secondary"
                  onClick={() => window.location.reload()}
                >
                  Try Again
                </Button>
                <Button
                  onClick={() =>
                    navigate("/supplier-management/config/categories")
                  }
                >
                  Back to Categories
                </Button>
              </div>
            </div>
          </div>
        </Container>
      </>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-x-4">
            <Button variant="transparent" onClick={handleCancel}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <Heading level="h2">Edit Category</Heading>
              <Text className="text-ui-fg-subtle">
                Update category information and configuration
              </Text>
            </div>
          </div>
        </div>

        {/* Breadcrumb Navigation */}
        <div className="px-6 py-3 bg-gray-50 border-b">
          <div className="flex items-center gap-2 text-sm">
            <button
              onClick={() => navigate("/supplier-management")}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              Supplier Management
            </button>
            <span className="text-gray-400">/</span>
            <span className="text-gray-600">Configuration</span>
            <span className="text-gray-400">/</span>
            <button
              onClick={handleCancel}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              Categories
            </button>
            <span className="text-gray-400">/</span>
            <span className="text-gray-900 font-medium">
              Edit {category?.name || "Category"}
            </span>
          </div>
        </div>

        {/* Form */}
        <div className="px-4 md:px-6 py-6">
          <form
            onSubmit={handleSubmit}
            className="space-y-6 md:space-y-8"
          >
            {/* Basic Information Section */}
            <div className="space-y-4">
              <Heading level="h3">Basic Information</Heading>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="e.g., Equipment, Guiding, Transportation"
                    className={errors.name ? "border-red-500" : ""}
                    disabled={loading}
                  />
                  {errors.name && (
                    <Text size="small" className="text-red-600 mt-1">
                      {errors.name}
                    </Text>
                  )}
                </div>

                <div>
                  <Label htmlFor="category_type">Category Type</Label>
                  <Select
                    value={formData.category_type}
                    onValueChange={(value) =>
                      handleInputChange("category_type", value)
                    }
                    disabled={loading}
                  >
                    <Select.Trigger>
                      <Select.Value />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Item value="Product">Product</Select.Item>
                      <Select.Item value="Service">Service</Select.Item>
                      <Select.Item value="Both">Both</Select.Item>
                    </Select.Content>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange("description", e.target.value)
                  }
                  placeholder="Enter description (optional)"
                  rows={3}
                  disabled={loading}
                />
              </div>

              <div>
                <Label htmlFor="icon">Icon</Label>
                <Input
                  id="icon"
                  value={formData.icon}
                  onChange={(e) => handleInputChange("icon", e.target.value)}
                  placeholder="e.g., 🏷️, 📦, 🎯 (emoji or icon name)"
                  disabled={loading}
                />
                <Text size="small" className="text-ui-fg-subtle mt-1">
                  Optional icon for visual identification in the UI
                </Text>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={formData.is_active}
                  onChange={(e) =>
                    handleInputChange("is_active", e.target.checked)
                  }
                  className="rounded border-gray-300"
                  disabled={loading}
                />
                <Label htmlFor="is_active">Active</Label>
              </div>
            </div>

            {/* Dynamic Field Schema Section */}
            <div className="space-y-4">
              <Heading level="h3">Dynamic Field Configuration</Heading>

              <DynamicFieldSchemaBuilder
                value={formData.dynamic_field_schema}
                onChange={(fields) =>
                  handleInputChange("dynamic_field_schema", fields)
                }
                error={errors.dynamic_field_schema}
                disabled={loading}
              />
            </div>

            {/* Actions */}
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center sm:justify-end gap-3 sm:gap-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="secondary"
                onClick={handleCancel}
                className="w-full sm:w-auto"
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                isLoading={loading}
                disabled={loading}
                className="w-full sm:w-auto"
              >
                Update Category
              </Button>
            </div>
          </form>
        </div>
      </Container>
      <Toaster />
    </>
  );
};

export default EditCategoryPage;
