import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Tag, PlusMini } from "@camped-ai/icons";
import {
  Edit,
  MoreHorizontal,
  Trash,
  Download,
  Upload,
  FileText,
  FileSpreadsheet,
  Eye,
  EyeOff,
  CheckCircle,
} from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Table,
  Badge,
  Toaster,
  toast,
  DropdownMenu,
  FocusModal,
  Select,
  Prompt,
} from "@camped-ai/ui";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../../hooks/use-rbac";
import {
  useCategories,
  useDeleteCategory,
  useUpdateCategory,
  useCategoryUsageCheck,
  useCategoryImportExport,
  type Category,
  type ImportValidationError,
  exportToCSV,
  exportToExcel,
} from "../../../../hooks/supplier-products-services/use-categories";

// Simple Progress Bar Component
const ProgressBarComponent = ({
  value,
  className = "",
}: {
  value: number;
  className?: string;
}) => {
  return (
    <div className={`w-full bg-gray-200 rounded-full h-2 ${className}`}>
      <div
        className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
        style={{ width: `${Math.min(100, Math.max(0, value))}%` }}
      />
    </div>
  );
};

const CategoriesConfigPage = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState<
    "Product" | "Service" | "Both" | ""
  >("");
  const [statusFilter, setStatusFilter] = useState<boolean | undefined>(
    undefined
  );
  const [sortBy, setSortBy] = useState<string>("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

  // Prompt modal state
  const [showInactivatePrompt, setShowInactivatePrompt] = useState(false);
  const [categoryToInactivate, setCategoryToInactivate] =
    useState<Category | null>(null);
  const [usageInfo, setUsageInfo] = useState<{
    productCount: number;
    serviceCount: number;
    totalCount: number;
  } | null>(null);

  // Import/Export state
  const [showImportModal, setShowImportModal] = useState(false);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importData, setImportData] = useState<any[]>([]);
  const [importErrors, setImportErrors] = useState<ImportValidationError[]>([]);
  const [importStep, setImportStep] = useState<
    "upload" | "preview" | "importing" | "complete"
  >("upload");

  // Hooks
  const { data: categoriesData, isLoading } = useCategories({
    search: searchTerm || undefined,
    category_type: typeFilter || undefined,
    is_active: statusFilter,
    sort_by: sortBy,
    sort_order: sortOrder,
    limit: 50,
  });
  const deleteCategory = useDeleteCategory();
  const updateCategory = useUpdateCategory();
  const categoryUsageCheck = useCategoryUsageCheck();

  // Use the import/export hook
  const {
    parseImportFile,
    importCategories,
    isImporting,
    importProgress,
    generateTemplate,
    exportToCSV: exportCSV,
    exportToExcel: exportExcel,
  } = useCategoryImportExport();

  const categories = categoriesData?.categories || [];
  const { hasPermission } = useRbac();

  const handleEdit = (category: Category) => {
    navigate(`/supplier-management/config/categories/${category.id}/edit`);
  };

  const handleInactivate = async (category: Category) => {
    try {
      // For activation, no need to check usage
      if (!category.is_active) {
        setCategoryToInactivate(category);
        setUsageInfo(null);
        setShowInactivatePrompt(true);
        return;
      }

      // For inactivation, check usage first
      const usageResult = await categoryUsageCheck.mutateAsync(category.id);

      const { canInactivate, usage } = usageResult;

      if (!canInactivate) {
        // Show detailed error message with guidance
        const errorMessage = `Cannot inactivate "${category.name}" because it has ${usage.activeCount} active item(s) (${usage.activeProductCount} product(s), ${usage.activeServiceCount} service(s)). Please inactivate these items first before inactivating the category.`;
        toast.error(errorMessage);
        return;
      }

      // Store the category and usage info for the prompt
      setCategoryToInactivate(category);
      setUsageInfo(usage);
      setShowInactivatePrompt(true);
    } catch (error) {
      console.error(`Error checking category usage:`, error);
      toast.error(
        error instanceof Error
          ? error.message
          : `Failed to check category usage`
      );
    }
  };

  const confirmInactivate = async () => {
    if (!categoryToInactivate) return;

    try {
      const newStatus = !categoryToInactivate.is_active;
      const action = newStatus ? "activate" : "inactivate";

      await updateCategory.mutateAsync({
        id: categoryToInactivate.id,
        data: { is_active: newStatus },
      });

      toast.success(`Category ${action}d successfully`);
      setShowInactivatePrompt(false);
      setCategoryToInactivate(null);
      setUsageInfo(null);
    } catch (error) {
      console.error(
        `Error ${
          categoryToInactivate.is_active ? "inactivating" : "activating"
        } category:`,
        error
      );
      toast.error(
        error instanceof Error
          ? error.message
          : `Failed to ${
              categoryToInactivate.is_active ? "inactivate" : "activate"
            } category`
      );
    }
  };

  const cancelInactivate = () => {
    setShowInactivatePrompt(false);
    setCategoryToInactivate(null);
    setUsageInfo(null);
  };

  const handleDelete = async (category: Category) => {
    if (window.confirm(`Are you sure you want to delete "${category.name}"?`)) {
      try {
        await deleteCategory.mutateAsync(category.id);
        toast.success("Category deleted successfully");
      } catch (error) {
        console.error("Error deleting category:", error);
        toast.error("Failed to delete category. Please try again.");
      }
    }
  };

  // Export functions
  const handleExport = async (format: "csv" | "excel") => {
    try {
      const exportData = categories.map((category) => ({
        name: category.name,
        description: category.description || "",
        category_type: category.category_type,
        icon: category.icon || "",
        is_active: category.is_active,
        dynamic_field_schema: category.dynamic_field_schema
          ? JSON.stringify(category.dynamic_field_schema)
          : "",
      }));

      const filename = `categories_export`;

      if (format === "csv") {
        exportCSV(exportData, filename);
      } else {
        exportExcel(exportData, filename);
      }

      toast.success(
        `Categories exported as ${format.toUpperCase()} successfully`
      );
    } catch (error) {
      console.error("Export error:", error);
      toast.error(`Failed to export as ${format.toUpperCase()}`);
    }
  };

  // Template export function
  const exportCategoryTemplate = (format: "csv" | "excel") => {
    const templateData = generateTemplate();
    const filename = `category_import_template`;

    if (format === "csv") {
      exportCSV(templateData, filename);
      toast.success("Template exported as CSV successfully");
    } else {
      exportExcel(templateData, filename);
      toast.success("Template exported as Excel (.xlsx) successfully");
    }
  };

  // Import functions
  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setImportFile(file);
    setImportStep("preview");

    try {
      const { data, errors } = await parseImportFile(file);
      setImportData(data);
      setImportErrors(errors);
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to parse file"
      );
      setImportStep("upload");
    }
  };

  const handleImport = async () => {
    if (importErrors.length > 0) {
      toast.error("Please fix validation errors before importing");
      return;
    }

    setImportStep("importing");

    try {
      await importCategories(importData);
      setImportStep("complete");
      setTimeout(() => {
        setShowImportModal(false);
        resetImportState();
      }, 2000);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Import failed");
      setImportStep("preview");
    }
  };

  const resetImportState = () => {
    setImportFile(null);
    setImportData([]);
    setImportErrors([]);
    setImportStep("upload");
  };

  const handleCloseImportModal = () => {
    setShowImportModal(false);
    resetImportState();
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        {/* Breadcrumb Navigation */}
        <div className="px-6 py-3 bg-gray-50 border-b">
          <div className="flex items-center gap-2 text-sm">
            <button
              onClick={() => navigate("/supplier-management")}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              Supplier Management
            </button>
            <span className="text-gray-400">/</span>
            <span className="text-gray-600">Configuration</span>
            <span className="text-gray-400">/</span>
            <span className="text-gray-900 font-medium">Categories</span>
          </div>
        </div>

        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between px-6 py-4 gap-4">
          <div>
            <Heading level="h2">Categories Configuration</Heading>
            <Text className="text-ui-fg-subtle">
              Manage product and service categories for supplier offerings
            </Text>
          </div>
          <div className="flex items-center gap-x-2 flex-wrap">
            {hasPermission("supplier_management:create") && (
              <Button
                size="small"
                onClick={() =>
                  navigate("/supplier-management/config/categories/create")
                }
              >
                <PlusMini />
                Add Category
              </Button>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="px-6 py-4 space-y-4 w-full">
          <div className="flex flex-col sm:flex-row gap-4 w-full">
            <div className="flex-1">
              <Input
                placeholder="Search categories..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>

            <div className="flex gap-4 sm:w-auto w-full">
              <div className="flex-1 sm:w-48">
                <Select
                  value={typeFilter || "all"}
                  onValueChange={(value) =>
                    setTypeFilter(
                      value === "all"
                        ? ""
                        : (value as "Product" | "Service" | "Both")
                    )
                  }
                >
                  <Select.Trigger className="w-full">
                    <Select.Value placeholder="Filter by Type" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="all">All Types</Select.Item>
                    <Select.Item value="Product">Product</Select.Item>
                    <Select.Item value="Service">Service</Select.Item>
                    <Select.Item value="Both">Both</Select.Item>
                  </Select.Content>
                </Select>
              </div>

              <div className="flex-1 sm:w-48">
                <Select
                  value={
                    statusFilter === undefined ? "all" : statusFilter.toString()
                  }
                  onValueChange={(value) => {
                    setStatusFilter(
                      value === "all" ? undefined : value === "true"
                    );
                  }}
                >
                  <Select.Trigger className="w-full">
                    <Select.Value placeholder="Filter by Status" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="all">All Status</Select.Item>
                    <Select.Item value="true">Active</Select.Item>
                    <Select.Item value="false">Inactive</Select.Item>
                  </Select.Content>
                </Select>
              </div>
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    if (sortBy === "name") {
                      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
                    } else {
                      setSortBy("name");
                      setSortOrder("asc");
                    }
                  }}
                >
                  <div className="flex items-center gap-1">
                    Category Name
                    <span className={`text-xs ${sortBy === "name" ? "text-blue-600" : "text-gray-400"}`}>
                      {sortBy === "name" ? (sortOrder === "asc" ? "↑" : "↓") : "↕"}
                    </span>
                  </div>
                </Table.HeaderCell>
                <Table.HeaderCell>Category Type</Table.HeaderCell>
                <Table.HeaderCell>Description</Table.HeaderCell>
                <Table.HeaderCell
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    if (sortBy === "total_count") {
                      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
                    } else {
                      setSortBy("total_count");
                      setSortOrder("desc");
                    }
                  }}
                >
                  <div className="flex items-center gap-1">
                    Number of Products/Services
                    <span className={`text-xs ${sortBy === "total_count" ? "text-blue-600" : "text-gray-400"}`}>
                      {sortBy === "total_count" ? (sortOrder === "asc" ? "↑" : "↓") : "↕"}
                    </span>
                  </div>
                </Table.HeaderCell>
                <Table.HeaderCell
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    if (sortBy === "is_active") {
                      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
                    } else {
                      setSortBy("is_active");
                      setSortOrder("desc");
                    }
                  }}
                >
                  <div className="flex items-center gap-1">
                    Status
                    <span className={`text-xs ${sortBy === "is_active" ? "text-blue-600" : "text-gray-400"}`}>
                      {sortBy === "is_active" ? (sortOrder === "asc" ? "↑" : "↓") : "↕"}
                    </span>
                  </div>
                </Table.HeaderCell>
                <Table.HeaderCell>Actions</Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {isLoading ? (
                Array.from({ length: 5 }).map((_, index) => (
                  <Table.Row key={`skeleton-${index}`}>
                    <Table.Cell>
                      <div className="h-4 bg-gray-200 rounded animate-pulse" />
                    </Table.Cell>
                    <Table.Cell>
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-16" />
                    </Table.Cell>
                    <Table.Cell>
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-32" />
                    </Table.Cell>
                    <Table.Cell>
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-20" />
                    </Table.Cell>
                    <Table.Cell>
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-16" />
                    </Table.Cell>
                    <Table.Cell>
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-8" />
                    </Table.Cell>
                  </Table.Row>
                ))
              ) : categories.length === 0 ? (
                <Table.Row>
                  <Table.Cell
                    className="text-center py-8"
                    style={{ gridColumn: "1 / -1" }}
                  >
                    <Text className="text-ui-fg-subtle">
                      {searchTerm
                        ? "No categories found matching your search"
                        : "No categories found"}
                    </Text>
                  </Table.Cell>
                </Table.Row>
              ) : (
                categories.map((category) => (
                  <Table.Row key={category.id}>
                    <Table.Cell>
                      <div className="flex items-center gap-2">
                        {(category as any).icon && (
                          <span className="text-lg">
                            {(category as any).icon}
                          </span>
                        )}
                        <Text weight="plus">{category.name}</Text>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge size="small">
                        {(category as any).category_type || "Both"}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <Text className="text-ui-fg-subtle max-w-xs truncate">
                        {category.description || "—"}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex flex-col gap-1">
                        <div className="flex items-center gap-2">
                          <Text size="small" weight="plus">
                            {category.total_count || 0} total
                          </Text>
                        </div>
                        <div className="flex items-center gap-2 text-xs text-ui-fg-subtle">
                          <span>{category.product_count || 0} products</span>
                          <span>•</span>
                          <span>{category.service_count || 0} services</span>
                        </div>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge color={category.is_active ? "green" : "grey"}>
                        {category.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <DropdownMenu>
                        <DropdownMenu.Trigger asChild>
                          <Button variant="transparent" size="small">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content align="end">
                          <DropdownMenu.Item
                            onClick={() =>
                              navigate(
                                `/supplier-management/config/categories/${category.id}`
                              )
                            }
                          >
                            <div className="flex items-center gap-2">
                              <Eye className="h-4 w-4" />
                              View
                            </div>
                          </DropdownMenu.Item>
                          <DropdownMenu.Item
                            onClick={() => handleEdit(category)}
                          >
                            <div className="flex items-center gap-2">
                              <Edit className="h-4 w-4" />
                              Edit
                            </div>
                          </DropdownMenu.Item>
                          <DropdownMenu.Item
                            onClick={() => handleInactivate(category)}
                          >
                            {category.is_active ? (
                              <div className="flex items-center gap-2">
                                <EyeOff className="h-4 w-4" />
                                Inactivate
                              </div>
                            ) : (
                              <div className="flex items-center gap-2">
                                <CheckCircle className="h-4 w-4" />
                                Activate
                              </div>
                            )}
                          </DropdownMenu.Item>
                        </DropdownMenu.Content>
                      </DropdownMenu>
                    </Table.Cell>
                  </Table.Row>
                ))
              )}
            </Table.Body>
          </Table>
        </div>
      </Container>

      {/* Import Modal */}
      <FocusModal open={showImportModal} onOpenChange={handleCloseImportModal}>
        <FocusModal.Content className="max-w-4xl max-h-[90vh]">
          <FocusModal.Header>
            <div className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              <Heading level="h2">Import Categories</Heading>
            </div>
          </FocusModal.Header>

          <FocusModal.Body className="overflow-y-auto space-y-6">
            {importStep === "upload" && (
              <div className="space-y-4">
                <div>
                  <Text className="text-ui-fg-subtle mb-4">
                    Upload a CSV or Excel file containing category data. Make
                    sure your file follows the template format.
                  </Text>

                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <input
                      type="file"
                      accept=".csv,.xlsx,.xls"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="file-upload"
                    />
                    <label htmlFor="file-upload" className="cursor-pointer">
                      <div className="space-y-2">
                        <Upload className="h-8 w-8 mx-auto text-gray-400" />
                        <Text>Click to upload or drag and drop</Text>
                        <Text className="text-sm text-ui-fg-subtle">
                          CSV, Excel (.xlsx, .xls)
                        </Text>
                      </div>
                    </label>
                  </div>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <Text className="font-medium text-blue-900 mb-2">
                    💡 Tips for successful import:
                  </Text>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>
                      • Download the template first to see the required format
                    </li>
                    <li>• Category names must be unique</li>
                    <li>• Dynamic field schema should be valid JSON</li>
                    <li>
                      • Use "Product", "Service", or "Both" for category_type
                    </li>
                  </ul>
                </div>
              </div>
            )}

            {importStep === "preview" && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Heading level="h3">Preview Import Data</Heading>
                  <Text className="text-sm text-ui-fg-subtle">
                    {importData.length} categories found
                  </Text>
                </div>

                {importErrors.length > 0 && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <Text className="font-medium text-red-900 mb-2">
                      ⚠️ Validation Errors ({importErrors.length})
                    </Text>
                    <div className="max-h-32 overflow-y-auto space-y-1">
                      {importErrors.map((error, index) => (
                        <Text key={index} className="text-sm text-red-800">
                          Row {error.row}: {error.field} - {error.message}
                        </Text>
                      ))}
                    </div>
                  </div>
                )}

                <div className="border rounded-lg overflow-hidden">
                  <div className="max-h-64 overflow-y-auto">
                    <Table>
                      <Table.Header>
                        <Table.Row>
                          <Table.HeaderCell>Name</Table.HeaderCell>
                          <Table.HeaderCell>Type</Table.HeaderCell>
                          <Table.HeaderCell>Description</Table.HeaderCell>
                          <Table.HeaderCell>Dynamic Fields</Table.HeaderCell>
                          <Table.HeaderCell>Status</Table.HeaderCell>
                        </Table.Row>
                      </Table.Header>
                      <Table.Body>
                        {importData.slice(0, 10).map((category, index) => (
                          <Table.Row key={index}>
                            <Table.Cell>{category.name}</Table.Cell>
                            <Table.Cell>
                              <Badge>{category.category_type || "Both"}</Badge>
                            </Table.Cell>
                            <Table.Cell className="max-w-xs truncate">
                              {category.description || "-"}
                            </Table.Cell>
                            <Table.Cell>
                              {category.dynamic_field_schema
                                ? `${
                                    Array.isArray(category.dynamic_field_schema)
                                      ? category.dynamic_field_schema.length
                                      : JSON.parse(
                                          category.dynamic_field_schema || "[]"
                                        ).length
                                  } fields`
                                : "None"}
                            </Table.Cell>
                            <Table.Cell>
                              <Badge
                                color={category.is_active ? "green" : "grey"}
                              >
                                {category.is_active ? "Active" : "Inactive"}
                              </Badge>
                            </Table.Cell>
                          </Table.Row>
                        ))}
                      </Table.Body>
                    </Table>
                  </div>
                  {importData.length > 10 && (
                    <div className="p-2 bg-gray-50 text-center">
                      <Text className="text-sm text-ui-fg-subtle">
                        ... and {importData.length - 10} more categories
                      </Text>
                    </div>
                  )}
                </div>
              </div>
            )}

            {importStep === "importing" && (
              <div className="space-y-4 text-center">
                <div className="space-y-2">
                  <Text className="font-medium">Importing Categories...</Text>
                  <Text className="text-sm text-ui-fg-subtle">
                    {importProgress.currentItem ||
                      `Processing ${importProgress.processed} of ${importProgress.total}`}
                  </Text>
                </div>
                <ProgressBarComponent value={importProgress.percentage} />
                <Text className="text-sm text-ui-fg-subtle">
                  {importProgress.percentage.toFixed(0)}% Complete
                </Text>
              </div>
            )}

            {importStep === "complete" && (
              <div className="text-center space-y-4">
                <div className="text-green-600">
                  <div className="text-4xl mb-2">✅</div>
                  <Text className="font-medium">
                    Import Completed Successfully!
                  </Text>
                  <Text className="text-sm text-ui-fg-subtle">
                    Categories have been imported and are now available.
                  </Text>
                </div>
              </div>
            )}
          </FocusModal.Body>

          <FocusModal.Footer>
            <div className="flex items-center gap-2">
              {importStep === "upload" && (
                <Button variant="secondary" onClick={handleCloseImportModal}>
                  Cancel
                </Button>
              )}

              {importStep === "preview" && (
                <>
                  <Button
                    variant="secondary"
                    onClick={() => setImportStep("upload")}
                  >
                    Back
                  </Button>
                  <Button
                    onClick={handleImport}
                    disabled={importErrors.length > 0}
                  >
                    Import {importData.length} Categories
                  </Button>
                </>
              )}

              {importStep === "importing" && (
                <Button disabled>Importing...</Button>
              )}

              {importStep === "complete" && (
                <Button onClick={handleCloseImportModal}>Close</Button>
              )}
            </div>
          </FocusModal.Footer>
        </FocusModal.Content>
      </FocusModal>

      {/* Inactivate/Activate Confirmation Prompt */}
      <Prompt
        open={showInactivatePrompt}
        onOpenChange={setShowInactivatePrompt}
      >
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>
              {categoryToInactivate?.is_active ? "Inactivate" : "Activate"}{" "}
              Category
            </Prompt.Title>
            <Prompt.Description>
              {categoryToInactivate?.is_active
                ? `Are you sure you want to inactivate "${categoryToInactivate.name}"? This category is not currently being used and can be safely inactivated.`
                : `Are you sure you want to activate "${categoryToInactivate?.name}"?`}
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={cancelInactivate}>Cancel</Prompt.Cancel>
            <Prompt.Action
              onClick={confirmInactivate}
              disabled={updateCategory.isPending}
            >
              {updateCategory.isPending
                ? categoryToInactivate?.is_active
                  ? "Inactivating..."
                  : "Activating..."
                : categoryToInactivate?.is_active
                ? "Inactivate"
                : "Activate"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>

      <Toaster />
    </>
  );
};

export default CategoriesConfigPage;
