import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Edit,
  MapPin,
  Building,
  ArrowLeft as ArrowLeftLucide,
  FileText,
  User,
  Trash2,
  Phone,
  Mail,
  Globe,
  Copy,
} from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Badge,
  Toaster,
  toast,
  IconButton,
  Prompt,
} from "@camped-ai/ui";
import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import {
  useSupplier,
  useDeleteSupplier,
} from "../../../../hooks/vendor-management/use-suppliers";
import { useCategories } from "../../../../hooks/supplier-products-services/use-categories";
import { useRbac } from "../../../../hooks/use-rbac";

import { DocumentUpload } from "../../../../components/vendor-management";
import {
  REGIONS,
  PAYMENT_METHODS,
  LANGUAGES,
} from "../../../../constants/supplier-form-options";
import {
  findOptionLabelWithFallback,
  getPayoutTermsLabel,
} from "../../../../utils/form-helpers";

const SupplierDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useRbac();

  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);

  // Copy to clipboard functionality
  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("Copied", {
        description: `${type} copied to clipboard`,
      });
    } catch (error) {
      console.error("Failed to copy:", error);
      toast.error("Failed to copy", {
        description: "Unable to copy to clipboard",
      });
    }
  };

  // Use the supplier management hooks
  const { data: supplierData, isLoading } = useSupplier(id!);
  const { data: categoriesData } = useCategories({ is_active: true });
  const deleteSupplier = useDeleteSupplier();
  const supplier: any = supplierData?.supplier;

  // Helper function to get status badge variant
  const getStatusBadgeVariant = (status: string): "green" | "red" | "grey" => {
    const normalizedStatus = status?.toLowerCase();
    switch (normalizedStatus) {
      case "active":
        return "green";
      case "inactive":
        return "red";
      default:
        return "grey";
    }
  };



  // Helper function to get category names from IDs
  const getCategoryNames = (categoryIds: string[]): string[] => {
    if (!categoriesData?.categories || !Array.isArray(categoryIds)) return [];
    return categoryIds
      .map((id) => {
        const category = categoriesData.categories.find((cat) => cat.id === id);
        return category ? category.name : null;
      })
      .filter((name): name is string => name !== null);
  };

  // Card Section Component
  const CardSection = ({
    title,
    subtitle,
    icon: Icon,
    children,
  }: {
    title: string;
    subtitle?: string;
    icon: any;
    children: React.ReactNode;
  }) => {
    return (
      <div className="bg-white rounded-lg border p-6 shadow-sm">
        <div className="border-b border-gray-200 pb-4 mb-6">
          <div className="flex items-center gap-3">
            <div>
              <Text weight="plus" className="text-base text-gray-900">{title}</Text>
              {subtitle && (
                <Text className="text-xs text-gray-600 mt-1">{subtitle}</Text>
              )}
            </div>
          </div>
        </div>
        <div>{children}</div>
      </div>
    );
  };

  const handleDeleteSupplier = () => {
    if (!supplier) return;
    setDeleteConfirmOpen(true);
  };

  const confirmDeleteSupplier = () => {
    if (!supplier) return;

    deleteSupplier.mutate(supplier.id, {
      onSuccess: () => {
        setDeleteConfirmOpen(false);
        // Navigate back to suppliers list
        navigate("/supplier-management/suppliers");
      },
    });
  };

  if (isLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <div className="h-screen flex items-center justify-center bg-background">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </>
    );
  }

  if (!supplier) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="py-8">
          <div className="text-center py-12 bg-muted rounded-lg">
            <Building className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <Text className="text-muted-foreground mb-4">
              Supplier not found
            </Text>
            <Button
              variant="primary"
              size="small"
              onClick={() => navigate("/supplier-management/suppliers")}
            >
              Back to Suppliers
            </Button>
          </div>
        </Container>
      </>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <div className="flex flex-row items-center justify-between">
        <IconButton onClick={() => navigate("/supplier-management/suppliers")}>
          <ArrowLeftLucide className="w-4 h-4" />
        </IconButton>

        <div className="flex flex-row items-center gap-2">
          {hasPermission("supplier_management:edit") && (
            <Button
              variant="secondary"
              size="small"
              onClick={() =>
                navigate(`/supplier-management/suppliers/${supplier.id}/edit`)
              }
            >
              <Edit className="w-4 h-4 mr-1" />
              Edit
            </Button>
          )}
          {hasPermission("supplier_management:delete") && (
            <Button
              variant="secondary"
              size="small"
              onClick={handleDeleteSupplier}
              className="text-red-600"
            >
              <Trash2 className="w-4 h-4 mr-2 " />
              Delete
            </Button>
          )}
        </div>
      </div>

      <Container className="py-6">
        {/* Header Section */}
        <div className="bg-white rounded-lg border p-6 shadow-sm mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div>
                <Heading level="h2">
                  {supplier.name}
                </Heading>
              </div>
              <Badge
                color={getStatusBadgeVariant(supplier.status)}
                className="px-3 py-1 text-sm font-medium rounded-full capitalize"
              >
                {supplier.status}
              </Badge>
            </div>
          </div>
        </div>

        {/* Main Content - Sectioned Layout */}
        <div className="space-y-6">
              {/* Section 1: Basic Information */}
              <CardSection
                title="Basic Information"
                subtitle="Core supplier details and identification"
                icon={Building}
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Text className="text-sm font-medium text-muted-foreground mb-1">
                      Supplier Type
                    </Text>
                    <Text>
                      {(supplier as any).supplier_type === "Company"
                        ? "Company"
                        : "Individual"}
                    </Text>
                  </div>

                  <div>
                    <Text className="text-sm font-medium text-muted-foreground mb-1">
                      Preference
                    </Text>
                    <div className="flex items-center gap-2">
                      <Text>
                        {(supplier as any).preference || "—"}
                      </Text>
                    </div>
                  </div>

                  <div>
                    <Text className="text-sm font-medium text-muted-foreground mb-1">
                      Website
                    </Text>
                    {supplier.website ? (
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-blue-600" />
                        <a
                          href={supplier.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline"
                        >
                          {supplier.website}
                        </a>
                      </div>
                    ) : (
                      <Text>—</Text>
                    )}
                  </div>
                </div>
              </CardSection>

              {/* Section 2: Business Information */}
              <CardSection
                title="Business Information"
                subtitle="Regional, operational, and financial details"
                icon={FileText}
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Text className="text-sm font-medium text-muted-foreground mb-1">
                      Preference
                    </Text>
                    <div className="flex items-center gap-2">
                      <Text>
                        {(supplier as any).preference || "—"}
                      </Text>
                    </div>
                  </div>

                  <div>
                    <Text className="text-sm font-medium text-muted-foreground mb-1">
                      Region
                    </Text>
                    <Text>
                      {findOptionLabelWithFallback(
                        REGIONS,
                        (supplier as any).region || ""
                      )}
                    </Text>
                  </div>

                  <div>
                    <Text className="text-sm font-medium text-muted-foreground mb-1">
                      Timezone
                    </Text>
                    <Text>{(supplier as any).timezone || "—"}</Text>
                  </div>

                  <div>
                    <Text className="text-sm font-medium text-muted-foreground mb-1">
                      Default Currency
                    </Text>
                    <Text>{(supplier as any).default_currency || "—"}</Text>
                  </div>

                  <div>
                    <Text className="text-sm font-medium text-muted-foreground mb-1">
                      Payment Method
                    </Text>
                    <Text>
                      {findOptionLabelWithFallback(
                        PAYMENT_METHODS,
                        (supplier as any).payment_method || ""
                      )}
                    </Text>
                  </div>

                  <div>
                    <Text className="text-sm font-medium text-muted-foreground mb-1">
                      Payment Terms
                    </Text>
                    <Text>
                      {getPayoutTermsLabel(
                        (supplier as any).payout_terms || ""
                      )}
                    </Text>
                  </div>

                  <div>
                    <Text className="text-sm font-medium text-muted-foreground mb-1">
                      Tax ID
                    </Text>
                    <div className="flex items-center gap-2">
                      <Text>{supplier.tax_id || "—"}</Text>
                      {supplier.tax_id && (
                        <Copy
                          className="h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600"
                          onClick={() =>
                            copyToClipboard(supplier.tax_id!, "Tax ID")
                          }
                        />
                      )}
                    </div>
                  </div>

                  <div>
                    <Text className="text-sm font-medium text-muted-foreground mb-2">
                      Language
                    </Text>
                    <div className="flex flex-wrap gap-2">
                      {(supplier as any).language_preference &&
                        (supplier as any).language_preference.length > 0 ? (
                        (supplier as any).language_preference.map((langCode: string) => {
                          const language = LANGUAGES.find(
                            (lang) =>
                              lang.value.toLowerCase() === langCode.toLowerCase() ||
                              lang.label.toLowerCase() === langCode.toLowerCase()
                          );
                          const displayName = language ? language.label : langCode;

                          return (
                            <Badge
                              key={langCode}
                              color="blue"
                              className="rounded-full text-xs px-2 py-1"
                            >
                              {displayName}
                            </Badge>
                          );
                        })
                      ) : (
                        <Text className="text-gray-500">No languages specified</Text>
                      )}
                    </div>
                  </div>
                </div>

                {(supplier as any).bank_account_details && (
                  <div className="mt-6">
                    <Text className="text-sm font-medium text-muted-foreground mb-3">
                      Bank Account Details
                    </Text>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center gap-2 justify-between">
                        <Text className="text-sm">
                          {(supplier as any).bank_account_details}
                        </Text>
                        <Copy
                          className="h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600"
                          onClick={() =>
                            copyToClipboard(
                              (supplier as any).bank_account_details!,
                              "Account details"
                            )
                          }
                        />
                      </div>
                    </div>
                  </div>
                )}

                {(supplier as any).categories &&
                  (supplier as any).categories.length > 0 && (
                    <div className="mt-6">
                      <Text className="text-sm font-medium text-muted-foreground mb-3">
                        Categories
                      </Text>
                      <div className="flex flex-wrap gap-2">
                        {(() => {
                          const categoryNames = getCategoryNames((supplier as any).categories || []);
                          return categoryNames.map((categoryName, index) => (
                            <Badge
                              key={index}
                              color="blue"
                              className="rounded-full text-sm px-3 py-1.5 font-medium"
                            >
                              {categoryName}
                            </Badge>
                          ));
                        })()}
                        {getCategoryNames((supplier as any).categories || []).length === 0 && (
                          <Text className="text-gray-400 text-sm">No categories assigned</Text>
                        )}
                      </div>
                    </div>
                  )}
              </CardSection>

              {/* Section 3: Address Information */}
              <CardSection
                title="Address Information"
                subtitle="Supplier location and contact address"
                icon={MapPin}
              >
                <div>
                  <Text className="text-sm font-medium text-muted-foreground mb-3">
                    Address Details
                  </Text>
                  <div className="flex items-start gap-3">
                    <MapPin className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div className="space-y-1">
                      {supplier.address ? (
                        <Text className="font-medium text-gray-900 whitespace-pre-line">
                          {supplier.address}
                        </Text>
                      ) : (
                        <Text className="text-gray-500">No address provided</Text>
                      )}
                    </div>
                  </div>
                </div>
              </CardSection>

              {/* Section 4: Contact Information */}
              <CardSection
                title="Contact Information"
                subtitle="Primary and secondary contact details"
                icon={User}
              >
                <div className="space-y-6">
                  {(supplier as any).contacts &&
                    (supplier as any).contacts.length > 0 ? (
                    (supplier as any).contacts.map(
                      (contact: any, index: number) => (
                        <div key={contact.id || index}>
                          <div className="flex items-center gap-2 mb-3">
                            <Text className="font-medium">{contact.name}</Text>
                            {contact.is_primary && (
                              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                Primary
                              </span>
                            )}
                            {contact.is_whatsapp && (
                              <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                WhatsApp
                              </span>
                            )}
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="flex items-center gap-2">
                              <Mail className="h-4 w-4 text-gray-400" />
                              <Text className="text-sm">{contact.email}</Text>
                              <Copy
                                className="h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600"
                                onClick={() =>
                                  copyToClipboard(contact.email, "Email")
                                }
                              />
                            </div>
                            {contact.phone_number && (
                              <div className="flex items-center gap-2">
                                <Phone className="h-4 w-4 text-gray-400" />
                                <Text className="text-sm">
                                  {contact.phone_number}
                                </Text>
                                <Copy
                                  className="h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600"
                                  onClick={() =>
                                    copyToClipboard(
                                      contact.phone_number,
                                      "Phone number"
                                    )
                                  }
                                />
                              </div>
                            )}
                          </div>
                        </div>
                      )
                    )
                  ) : (
                    <div className="text-center py-8">
                      <Text className="text-muted-foreground">
                        No contact information available
                      </Text>
                    </div>
                  )}
                </div>
              </CardSection>

          {/* Section 5: Documents */}
          <CardSection
            title="Documents"
            subtitle="Uploaded files and attachments"
            icon={FileText}
          >
            <DocumentUpload supplierId={id!} />
          </CardSection>
        </div>
      </Container>

      {/* Delete Confirmation Prompt */}
      <Prompt open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Supplier</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete "{supplier?.name}"? This action
              cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={() => setDeleteConfirmOpen(false)}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action
              onClick={confirmDeleteSupplier}
              disabled={deleteSupplier.isPending}
            >
              {deleteSupplier.isPending ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Supplier Details",
});

export default SupplierDetailPage;
