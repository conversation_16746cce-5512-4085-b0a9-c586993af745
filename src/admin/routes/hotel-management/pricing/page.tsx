import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { <PERSON>Left, Tags, Building2 } from "lucide-react";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import HotelPricingManager from "../../../components/hotel/pricing/hotel-pricing-manager-new";
import HotelSelector from "../../../components/hotel/hotel-selector";
import { useRbac } from "../../../hooks/use-rbac";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { useHotels } from "../../../hooks/supplier-products-services/use-hotels";

interface Hotel {
  id: string;
  name: string;
}

interface RoomConfig {
  id: string;
  title: string;
  handle?: string;
  description?: string;
}

interface ComprehensivePricingData {
  hotel: Hotel;
  room_configs: RoomConfig[];
  occupancy_configs: any[];
  meal_plans: any[];
  room_pricing_data: any[];
}

const HotelPricingPage = () => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [selectedHotelId, setSelectedHotelId] = useState<string | null>(null);
  const [pricingData, setPricingData] = useState<ComprehensivePricingData | null>(null);
  const [isLoadingPricing, setIsLoadingPricing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get hotels list for default selection
  const { data: hotelsData, isLoading: isLoadingHotels } = useHotels({
    limit: 100,
    is_active: true,
  });

  // Set default hotel selection when hotels are loaded
  useEffect(() => {
    if (hotelsData?.hotels && hotelsData.hotels.length > 0 && !selectedHotelId) {
      const firstHotel = hotelsData.hotels[0];
      setSelectedHotelId(firstHotel.id);
    }
  }, [hotelsData, selectedHotelId]);

  // Fetch comprehensive pricing data when hotel is selected
  const fetchComprehensivePricing = async (hotelId: string) => {
    setIsLoadingPricing(true);
    setError(null);

    try {
      const response = await fetch(
        `/admin/hotel-management/hotels/${hotelId}/pricing`,
        {
          credentials: "include",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch comprehensive pricing data");
      }

      const data = await response.json();
      setPricingData(data);
    } catch (error) {
      console.error("Error fetching comprehensive pricing data:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to load pricing data";
      setError(errorMessage);
      toast.error("Error", {
        description: errorMessage,
      });
    } finally {
      setIsLoadingPricing(false);
    }
  };

  // Fetch pricing data when hotel is selected
  useEffect(() => {
    if (selectedHotelId) {
      fetchComprehensivePricing(selectedHotelId);
    }
  }, [selectedHotelId]);

  // Handle hotel selection change
  const handleHotelChange = (hotelId: string) => {
    setSelectedHotelId(hotelId);
    setPricingData(null); // Clear previous data while loading
  };

  // Check permissions first
  if (!hasPermission("pricing:view")) {
    return (
      <Container className="py-8">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Tags className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">
            You don't have permission to view pricing information
          </Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate("/hotel-management/hotels")}
          >
            Back to Hotels
          </Button>
        </div>
      </Container>
    );
  }

  // Show loading state while hotels are being fetched
  if (isLoadingHotels) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Show error state if no hotels available
  if (!hotelsData?.hotels || hotelsData.hotels.length === 0) {
    return (
      <Container className="py-8">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">No hotels found</Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate("/hotel-management/hotels")}
          >
            Back to Hotels
          </Button>
        </div>
      </Container>
    );
  }

  const canEdit = hasPermission("pricing:edit");
  const canCreate = hasPermission("pricing:create");
  const canDelete = hasPermission("pricing:delete");

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <Container className="py-6">
        {/* Header with hotel selector */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <Heading level="h1" className="text-2xl font-bold">
                Hotel Pricing Management
              </Heading>
            </div>
          </div>

          {/* Hotel Selector */}
          <div className="max-w-md">
            <HotelSelector
              selectedHotelId={selectedHotelId}
              onHotelChange={handleHotelChange}
              label="Select Hotel"
              placeholder="Choose a hotel to manage pricing..."
              showActiveOnly={true}
            />
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="mb-6 p-4 border border-red-200 rounded-md bg-red-50">
            <div className="flex items-center space-x-2">
              <Tags className="h-5 w-5 text-red-500" />
              <Text className="text-red-700 font-medium">Error Loading Pricing Data</Text>
            </div>
            <Text className="text-red-600 mt-1">{error}</Text>
            <Button
              variant="secondary"
              size="small"
              onClick={() => selectedHotelId && fetchComprehensivePricing(selectedHotelId)}
              className="mt-3"
            >
              Retry
            </Button>
          </div>
        )}

        {/* Loading State */}
        {isLoadingPricing && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
              <Text className="text-gray-600">Loading pricing data...</Text>
            </div>
          </div>
        )}

        {/* Pricing Manager */}
        {selectedHotelId && pricingData && !isLoadingPricing && !error && (
          <HotelPricingManager
            hotelId={selectedHotelId}
            hotelName={pricingData.hotel.name}
            roomConfigs={pricingData.room_configs}
            onBack={() => navigate("/hotel-management/hotels")}
            canEdit={canEdit}
            canCreate={canCreate}
            canDelete={canDelete}
            hideBackButton={true}
          />
        )}

        {/* No Hotel Selected State */}
        {!selectedHotelId && !isLoadingPricing && (
          <div className="text-center py-12 bg-muted rounded-lg">
            <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <Text className="text-muted-foreground mb-4">
              Please select a hotel to view and manage pricing
            </Text>
          </div>
        )}
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Hotel Pricing",
  icon: Tags,
});

export default HotelPricingPage;
