import React from "react";
import { Input, Label, Select, Text, Heading, Badge } from "@camped-ai/ui";
import { X, ChevronDown } from "lucide-react";
import { useHotels } from "../../hooks/supplier-products-services/use-hotels";
import { useDestinations } from "../../hooks/supplier-products-services/use-destinations";
import { useProductsServices } from "../../hooks/supplier-products-services/use-products-services";

export interface DynamicFieldSchema {
  label: string;
  key: string;
  type:
    | "text"
    | "number"
    | "dropdown"
    | "multi-select"
    | "date"
    | "boolean"
    | "number-range"
    | "hotels"
    | "destinations"
    | "addons";
  options?: string[];
  required: boolean;
  used_in_filtering?: boolean;
  used_in_supplier_offering?: boolean;
  used_in_product?: boolean;
  locked_in_offerings?: boolean;
  field_context?: "supplier" | "customer"; // NEW: Context filtering for fields
}

interface DynamicFieldRendererProps {
  schema: DynamicFieldSchema[];
  values: Record<string, any>;
  onChange: (key: string, value: any) => void;
  errors?: Record<string, string>;
  disabled?: boolean;
  inheritedValues?: Record<string, any>; // Values inherited from product/service
  showInheritanceIndicators?: boolean; // Whether to show inheritance indicators
  fieldContext?: "supplier" | "customer"; // NEW: Filter fields by context (default: show all)
  onAddonSelectionChange?: (
    fieldKey: string,
    selectedAddonIds: string[]
  ) => void; // Callback for addon selection changes
}

const DynamicFieldRenderer: React.FC<DynamicFieldRendererProps> = ({
  schema,
  values,
  onChange,
  errors = {},
  disabled = false,
  inheritedValues = {},
  showInheritanceIndicators = false,
  fieldContext, // NEW: Context filter
  onAddonSelectionChange,
}) => {
  // Fetch hotels, destinations, and product services data
  const { data: hotelsResponse } = useHotels({ is_active: true });
  const { data: destinationsResponse } = useDestinations({ is_active: true });
  const { data: productServicesResponse } = useProductsServices({
    status: "active",
    limit: 1000,
  });

  const hotels = hotelsResponse?.hotels || [];
  const destinations = destinationsResponse?.destinations || [];
  const productServices = productServicesResponse?.product_services || [];

  if (!schema || schema.length === 0) {
    return null;
  }

  // Filter schema by field context if specified
  const filteredSchema = fieldContext
    ? schema.filter(
        (field) => !field.field_context || field.field_context === fieldContext
      )
    : schema;

  if (filteredSchema.length === 0) {
    return null;
  }

  const renderFieldLabel = (field: DynamicFieldSchema) => {
    const isLocked = field.locked_in_offerings && showInheritanceIndicators;
    return (
      <div className="flex items-center gap-2">
        <Label htmlFor={`custom_field_${field.key}`}>
          {field.label}
          {field.required && <span className="text-red-500 ml-1">*</span>}
        </Label>
        {isLocked && (
          <Badge variant="orange" size="small">
            Inherited
          </Badge>
        )}
      </div>
    );
  };

  // State for dropdowns - moved outside renderField to avoid hooks rules violations
  const [dropdownStates, setDropdownStates] = React.useState<
    Record<string, boolean>
  >({});
  const dropdownRefs = React.useRef<Record<string, HTMLDivElement | null>>({});

  // Close dropdowns when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      Object.entries(dropdownRefs.current).forEach(([key, ref]) => {
        if (ref && !ref.contains(event.target as Node)) {
          setDropdownStates((prev) => ({ ...prev, [key]: false }));
        }
      });
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const toggleDropdown = (fieldKey: string) => {
    setDropdownStates((prev) => ({ ...prev, [fieldKey]: !prev[fieldKey] }));
  };

  const renderField = (field: DynamicFieldSchema) => {
    const isLocked = field.locked_in_offerings && showInheritanceIndicators;
    const inheritedValue = inheritedValues[field.key];
    const displayValue =
      isLocked && inheritedValue !== undefined
        ? inheritedValue
        : values[field.key];
    const error = errors[field.key];
    const fieldId = `custom_field_${field.key}`;
    const isFieldDisabled = disabled || isLocked;

    switch (field.type) {
      case "text":
        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <Input
              id={fieldId}
              value={displayValue || ""}
              onChange={(e) => onChange(field.key, e.target.value)}
              placeholder={`Enter ${field.label.toLowerCase()}`}
              disabled={isFieldDisabled}
              className={error ? "border-red-500" : ""}
            />
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "number":
        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <Input
              id={fieldId}
              type="number"
              value={displayValue || ""}
              onChange={(e) =>
                onChange(
                  field.key,
                  e.target.value ? parseFloat(e.target.value) : ""
                )
              }
              placeholder={`Enter ${field.label.toLowerCase()}`}
              disabled={isFieldDisabled}
              className={error ? "border-red-500" : ""}
            />
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "dropdown":
        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <Select
              value={displayValue || ""}
              onValueChange={(selectedValue) =>
                onChange(field.key, selectedValue)
              }
              disabled={isFieldDisabled}
            >
              <Select.Trigger className={error ? "border-red-500" : ""}>
                <Select.Value
                  placeholder={`Select ${field.label.toLowerCase()}`}
                />
              </Select.Trigger>
              <Select.Content>
                {field.options?.map((option) => (
                  <Select.Item key={option} value={option}>
                    {option}
                  </Select.Item>
                ))}
              </Select.Content>
            </Select>
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "multi-select":
        const selectedValues = Array.isArray(displayValue) ? displayValue : [];
        // Sort selected values alphabetically for consistent display
        const sortedSelectedValues = [...selectedValues].sort();
        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <Select
              value=""
              onValueChange={(selectedValue) => {
                if (selectedValue && !selectedValues.includes(selectedValue)) {
                  onChange(field.key, [...selectedValues, selectedValue]);
                }
              }}
              disabled={isFieldDisabled}
            >
              <Select.Trigger className={error ? "border-red-500" : ""}>
                <Select.Value
                  placeholder={`Select ${field.label.toLowerCase()}`}
                />
              </Select.Trigger>
              <Select.Content>
                {field.options
                  ?.filter((option) => !selectedValues.includes(option))
                  .sort() // Sort options alphabetically
                  .map((option) => (
                    <Select.Item key={option} value={option}>
                      {option}
                    </Select.Item>
                  ))}
              </Select.Content>
            </Select>
            {sortedSelectedValues.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {sortedSelectedValues.map((selectedValue) => (
                  <div
                    key={selectedValue}
                    className="flex items-center gap-1 px-2 py-1 bg-gray-100 rounded-md text-sm"
                  >
                    {selectedValue}
                    <button
                      type="button"
                      onClick={() => {
                        onChange(
                          field.key,
                          selectedValues.filter((v) => v !== selectedValue)
                        );
                      }}
                      className="ml-1 text-gray-500 hover:text-red-500"
                      disabled={isFieldDisabled}
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "date":
        // Ensure date value is in YYYY-MM-DD format for HTML date input
        let dateValue = "";
        if (displayValue) {
          if (typeof displayValue === "string") {
            // Try to parse and format the date
            const date = new Date(displayValue);
            if (!isNaN(date.getTime())) {
              dateValue = date.toISOString().split("T")[0]; // YYYY-MM-DD format
            } else {
              dateValue = displayValue; // Use as-is if it's already in correct format
            }
          }
        }

        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <Input
              id={fieldId}
              type="date"
              value={dateValue}
              onChange={(e) => onChange(field.key, e.target.value)}
              disabled={isFieldDisabled}
              className={error ? "border-red-500" : ""}
            />
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "boolean":
        const isLocked = field.locked_in_offerings && showInheritanceIndicators;
        return (
          <div key={field.key} className="space-y-2">
            <div className="flex items-center gap-2">
              <input
                id={fieldId}
                type="checkbox"
                checked={displayValue || false}
                onChange={(e) => onChange(field.key, e.target.checked)}
                disabled={isFieldDisabled}
                className="rounded border-gray-300"
              />
              <Label htmlFor={fieldId}>
                {field.label}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </Label>
              {isLocked && (
                <Badge variant="orange" size="small">
                  Inherited
                </Badge>
              )}
            </div>
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "number-range":
        // Handle both object format {min: x, max: y} and string format "x-y"
        let rangeValue = { min: "", max: "" };
        if (displayValue) {
          if (
            typeof displayValue === "object" &&
            displayValue.min !== undefined &&
            displayValue.max !== undefined
          ) {
            rangeValue = displayValue;
          } else if (
            typeof displayValue === "string" &&
            displayValue.includes("-")
          ) {
            const [min, max] = displayValue.split("-");
            rangeValue = { min: min.trim(), max: max.trim() };
          } else if (typeof displayValue === "string") {
            // If it's just a single number, put it in min
            rangeValue = { min: displayValue, max: "" };
          }
        }

        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <div className="flex items-center gap-2">
              <Input
                type="number"
                value={rangeValue.min || ""}
                onChange={(e) =>
                  onChange(field.key, {
                    ...rangeValue,
                    min: e.target.value ? parseFloat(e.target.value) : "",
                  })
                }
                placeholder="Min"
                disabled={isFieldDisabled}
                className={`flex-1 ${error ? "border-red-500" : ""}`}
              />
              <Text className="text-ui-fg-subtle">to</Text>
              <Input
                type="number"
                value={rangeValue.max || ""}
                onChange={(e) =>
                  onChange(field.key, {
                    ...rangeValue,
                    max: e.target.value ? parseFloat(e.target.value) : "",
                  })
                }
                placeholder="Max"
                disabled={isFieldDisabled}
                className={`flex-1 ${error ? "border-red-500" : ""}`}
              />
            </div>
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "hotels":
        const selectedHotels = Array.isArray(displayValue) ? displayValue : [];
        const isHotelsOpen = dropdownStates[`hotels_${field.key}`] || false;

        const handleHotelToggle = (hotelId: string) => {
          if (isFieldDisabled) return;

          const newSelection = selectedHotels.includes(hotelId)
            ? selectedHotels.filter((id) => id !== hotelId)
            : [...selectedHotels, hotelId];

          onChange(field.key, newSelection);
        };

        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <div
              className="relative z-10"
              ref={(el) => {
                dropdownRefs.current[`hotels_${field.key}`] = el;
              }}
              style={{ position: "relative", zIndex: 10 }}
            >
              {/* Dropdown Trigger */}
              <button
                type="button"
                onClick={() =>
                  !isFieldDisabled && toggleDropdown(`hotels_${field.key}`)
                }
                disabled={isFieldDisabled}
                className={`
                  w-full flex items-center justify-between px-4 py-3 text-left
                  border-2 rounded-lg shadow-sm transition-all duration-200
                  bg-gray-50 dark:bg-gray-800
                  ${
                    isFieldDisabled
                      ? "bg-gray-100 dark:bg-gray-700 cursor-not-allowed opacity-50 border-gray-200"
                      : "hover:bg-white hover:border-blue-300 cursor-pointer border-gray-200"
                  }
                  ${isHotelsOpen ? "border-blue-500 bg-white shadow-md" : ""}
                  ${error ? "border-red-500" : ""}
                `}
              >
                <Text
                  className={`${
                    selectedHotels.length === 0
                      ? "text-gray-400"
                      : "text-gray-700"
                  } font-medium`}
                >
                  {selectedHotels.length === 0
                    ? "Select hotels"
                    : `${selectedHotels.length} selected`}
                </Text>
                <ChevronDown
                  className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${
                    isHotelsOpen ? "rotate-180" : ""
                  }`}
                />
              </button>

              {/* Dropdown Menu */}
              {isHotelsOpen && (
                <div
                  className="absolute top-full left-0 right-0 z-[9999] mt-1 bg-white dark:bg-gray-800 border border-gray-200 rounded-lg shadow-xl max-h-60 overflow-auto"
                  style={{
                    position: "absolute",
                    zIndex: 9999,
                    top: "100%",
                    left: 0,
                    right: 0,
                    marginTop: "4px",
                  }}
                >
                  {hotels.length === 0 ? (
                    <div className="px-4 py-3 text-gray-500 text-sm text-center">
                      No hotels available
                    </div>
                  ) : (
                    hotels.map((hotel) => (
                      <button
                        key={hotel.id}
                        type="button"
                        onClick={() => handleHotelToggle(hotel.id)}
                        className="w-full px-4 py-3 text-left hover:bg-blue-50 dark:hover:bg-gray-700 flex items-center gap-3 transition-colors duration-150 border-b border-gray-100 last:border-b-0"
                      >
                        <input
                          type="checkbox"
                          checked={selectedHotels.includes(hotel.id)}
                          onChange={() => {}} // Handled by button click
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 w-4 h-4"
                        />
                        <span className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                          {hotel.name}
                        </span>
                      </button>
                    ))
                  )}
                </div>
              )}
            </div>

            {/* Selected Tags Container */}
            {selectedHotels.length > 0 && (
              <div className="mt-2 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200">
                <div className="flex flex-wrap gap-2">
                  {selectedHotels.map((hotelId) => {
                    const hotel = hotels.find((h) => h.id === hotelId);
                    return hotel ? (
                      <span
                        key={hotelId}
                        className="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium bg-white text-gray-800 border border-gray-200 shadow-sm"
                      >
                        {hotel.name}
                        {!isFieldDisabled && (
                          <button
                            type="button"
                            onClick={() => handleHotelToggle(hotelId)}
                            className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-gray-400 hover:bg-gray-100 hover:text-gray-600 transition-colors duration-150"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        )}
                      </span>
                    ) : null;
                  })}
                </div>
              </div>
            )}

            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "destinations":
        const selectedDestinations = Array.isArray(displayValue)
          ? displayValue
          : [];
        const isDestinationsOpen =
          dropdownStates[`destinations_${field.key}`] || false;

        const handleDestinationToggle = (destinationId: string) => {
          if (isFieldDisabled) return;

          const newSelection = selectedDestinations.includes(destinationId)
            ? selectedDestinations.filter((id) => id !== destinationId)
            : [...selectedDestinations, destinationId];

          onChange(field.key, newSelection);
        };

        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <div
              className="relative z-10"
              ref={(el) => {
                dropdownRefs.current[`destinations_${field.key}`] = el;
              }}
              style={{ position: "relative", zIndex: 10 }}
            >
              {/* Dropdown Trigger */}
              <button
                type="button"
                onClick={() =>
                  !isFieldDisabled &&
                  toggleDropdown(`destinations_${field.key}`)
                }
                disabled={isFieldDisabled}
                className={`
                  w-full flex items-center justify-between px-4 py-3 text-left
                  border-2 rounded-lg shadow-sm transition-all duration-200
                  bg-gray-50 dark:bg-gray-800
                  ${
                    isFieldDisabled
                      ? "bg-gray-100 dark:bg-gray-700 cursor-not-allowed opacity-50 border-gray-200"
                      : "hover:bg-white hover:border-blue-300 cursor-pointer border-gray-200"
                  }
                  ${
                    isDestinationsOpen
                      ? "border-blue-500 bg-white shadow-md"
                      : ""
                  }
                  ${error ? "border-red-500" : ""}
                `}
              >
                <Text
                  className={`${
                    selectedDestinations.length === 0
                      ? "text-gray-400"
                      : "text-gray-700"
                  } font-medium`}
                >
                  {selectedDestinations.length === 0
                    ? "Select destinations"
                    : `${selectedDestinations.length} selected`}
                </Text>
                <ChevronDown
                  className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${
                    isDestinationsOpen ? "rotate-180" : ""
                  }`}
                />
              </button>

              {/* Dropdown Menu */}
              {isDestinationsOpen && (
                <div
                  className="absolute top-full left-0 right-0 z-[9999] mt-1 bg-white dark:bg-gray-800 border border-gray-200 rounded-lg shadow-xl max-h-60 overflow-auto"
                  style={{
                    position: "absolute",
                    zIndex: 9999,
                    top: "100%",
                    left: 0,
                    right: 0,
                    marginTop: "4px",
                  }}
                >
                  {destinations.length === 0 ? (
                    <div className="px-4 py-3 text-gray-500 text-sm text-center">
                      No destinations available
                    </div>
                  ) : (
                    destinations.map((destination) => (
                      <button
                        key={destination.id}
                        type="button"
                        onClick={() => handleDestinationToggle(destination.id)}
                        className="w-full px-4 py-3 text-left hover:bg-blue-50 dark:hover:bg-gray-700 flex items-center gap-3 transition-colors duration-150 border-b border-gray-100 last:border-b-0"
                      >
                        <input
                          type="checkbox"
                          checked={selectedDestinations.includes(
                            destination.id
                          )}
                          onChange={() => {}} // Handled by button click
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 w-4 h-4"
                        />
                        <span className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                          {destination.name}
                        </span>
                      </button>
                    ))
                  )}
                </div>
              )}
            </div>

            {/* Selected Tags Container */}
            {selectedDestinations.length > 0 && (
              <div className="mt-2 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200">
                <div className="flex flex-wrap gap-2">
                  {selectedDestinations.map((destId) => {
                    const destination = destinations.find(
                      (d) => d.id === destId
                    );
                    return destination ? (
                      <span
                        key={destId}
                        className="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium bg-white text-gray-800 border border-gray-200 shadow-sm"
                      >
                        {destination.name}
                        {!isFieldDisabled && (
                          <button
                            type="button"
                            onClick={() => handleDestinationToggle(destId)}
                            className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-gray-400 hover:bg-gray-100 hover:text-gray-600 transition-colors duration-150"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        )}
                      </span>
                    ) : null;
                  })}
                </div>
              </div>
            )}

            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "addons":
        const selectedAddons = Array.isArray(displayValue) ? displayValue : [];
        const isAddonsOpen = dropdownStates[`addons_${field.key}`] || false;

        const handleAddonToggle = (addonId: string) => {
          if (isFieldDisabled) return;

          const newSelection = selectedAddons.includes(addonId)
            ? selectedAddons.filter((id) => id !== addonId)
            : [...selectedAddons, addonId];

          onChange(field.key, newSelection);

          // Notify parent component about addon selection change
          if (onAddonSelectionChange) {
            onAddonSelectionChange(field.key, newSelection);
          }
        };

        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <div
              className="relative z-10"
              ref={(el) => {
                dropdownRefs.current[`addons_${field.key}`] = el;
              }}
              style={{ position: "relative", zIndex: 10 }}
            >
              {/* Dropdown Trigger */}
              <button
                type="button"
                onClick={() =>
                  !isFieldDisabled && toggleDropdown(`addons_${field.key}`)
                }
                disabled={isFieldDisabled}
                className={`
                  w-full flex items-center justify-between px-4 py-3 text-left
                  border-2 rounded-lg shadow-sm transition-all duration-200
                  bg-gray-50 dark:bg-gray-800
                  ${
                    isFieldDisabled
                      ? "bg-gray-100 dark:bg-gray-700 cursor-not-allowed opacity-50 border-gray-200"
                      : "hover:bg-white hover:border-blue-300 cursor-pointer border-gray-200"
                  }
                  ${isAddonsOpen ? "border-blue-500 bg-white shadow-md" : ""}
                  ${error ? "border-red-500" : ""}
                `}
              >
                <Text
                  className={`${
                    selectedAddons.length === 0
                      ? "text-gray-400"
                      : "text-gray-700"
                  } font-medium`}
                >
                  {selectedAddons.length === 0
                    ? "Select add-ons"
                    : `${selectedAddons.length} selected`}
                </Text>
                <ChevronDown
                  className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${
                    isAddonsOpen ? "rotate-180" : ""
                  }`}
                />
              </button>

              {/* Dropdown Menu */}
              {isAddonsOpen && (
                <div
                  className="absolute top-full left-0 right-0 z-[9999] mt-1 bg-white dark:bg-gray-800 border border-gray-200 rounded-lg shadow-xl max-h-60 overflow-auto"
                  style={{
                    position: "absolute",
                    zIndex: 9999,
                    top: "100%",
                    left: 0,
                    right: 0,
                    marginTop: "4px",
                  }}
                >
                  {productServices.length === 0 ? (
                    <div className="px-4 py-3 text-gray-500 text-sm text-center">
                      No add-ons available
                    </div>
                  ) : (
                    productServices.map((addon) => (
                      <button
                        key={addon.id}
                        type="button"
                        onClick={() => handleAddonToggle(addon.id)}
                        className="w-full px-4 py-3 text-left hover:bg-blue-50 dark:hover:bg-gray-700 flex items-center gap-3 transition-colors duration-150 border-b border-gray-100 last:border-b-0"
                      >
                        <input
                          type="checkbox"
                          checked={selectedAddons.includes(addon.id)}
                          onChange={() => {}} // Handled by button click
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 w-4 h-4"
                        />
                        <div className="flex-1 min-w-0">
                          <span className="text-sm text-gray-900 dark:text-gray-100 font-medium block truncate">
                            {addon.name}
                          </span>
                          {addon.category?.name && (
                            <span className="text-xs text-gray-500 block">
                              {addon.category.name} • {addon.type}
                            </span>
                          )}
                        </div>
                      </button>
                    ))
                  )}
                </div>
              )}
            </div>

            {/* Selected Tags Container */}
            {selectedAddons.length > 0 && (
              <div className="mt-2 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200">
                <div className="flex flex-wrap gap-2">
                  {selectedAddons.map((addonId) => {
                    const addon = productServices.find(
                      (ps) => ps.id === addonId
                    );
                    return addon ? (
                      <span
                        key={addonId}
                        className="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium bg-white text-gray-800 border border-gray-200 shadow-sm"
                      >
                        <span
                          className="truncate max-w-[200px]"
                          title={addon.name}
                        >
                          {addon.name}
                        </span>
                        {!isFieldDisabled && (
                          <button
                            type="button"
                            onClick={() => handleAddonToggle(addonId)}
                            className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-gray-400 hover:bg-gray-100 hover:text-gray-600 transition-colors duration-150"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        )}
                      </span>
                    ) : null;
                  })}
                </div>
              </div>
            )}

            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      <div className="mb-6">
        <Heading level="h3" className="text-lg font-medium">
          Custom Fields
        </Heading>
        <Text className="text-ui-fg-subtle text-sm">
          Additional information specific to this category
        </Text>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {filteredSchema.map(renderField)}
      </div>
    </div>
  );
};

export default DynamicFieldRenderer;
