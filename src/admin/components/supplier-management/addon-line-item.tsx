import React from "react";
import { <PERSON><PERSON>, Text } from "@camped-ai/ui";
import { Plus } from "lucide-react";

export interface AddonLineItem {
  id: string; // unique line item ID
  addon_id?: string; // null for manual entries
  name: string;
  supplier_name?: string;

  // Complete supplier offering pricing data
  gross_price?: number;
  commission?: number;
  net_cost?: number; // Calculated: Gross Price - (Gross Price × Commission)
  net_cost_manual?: number; // Manual override for net cost
  margin_rate?: number;
  selling_price?: number;
  currency?: string;
  selling_currency?: string;
  exchange_rate?: number;
  selling_price_selling_currency?: number;

  // Addon requirement configuration
  is_mandatory?: boolean; // Whether this addon is mandatory for bookings

  is_auto_populated: boolean;
  is_manual: boolean;
}

interface AddonLineItemProps {
  item: AddonLineItem;
  onAddToNetCost: (id: string) => void;
  disabled?: boolean;
  currency?: string;
}

const AddonLineItemComponent: React.FC<AddonLineItemProps> = ({
  item,
  onAddToNetCost,
  disabled = false,
  currency = "CHF",
}) => {
  const handleAddToNetCost = () => {
    onAddToNetCost(item.id);
  };

  return (
    <div className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-150">
      {/* Addon Info */}
      <div className="flex-1">
        <div className="flex items-center gap-4">
          {/* Addon Name */}
          <div className="flex-1">
            <Text className="font-medium text-gray-900">{item.name}</Text>
            {item.is_auto_populated && (
              <Text size="small" className="text-gray-500">
                Auto-populated from selection
              </Text>
            )}
          </div>

          {/* Supplier */}
          <div className="w-40">
            <Text
              size="small"
              className="text-gray-500 uppercase tracking-wide"
            >
              Supplier
            </Text>
            <Text className="font-medium">
              {item.supplier_name || "Unknown Supplier"}
            </Text>
          </div>

          {/* Price */}
          <div className="w-32 text-right">
            <Text
              size="small"
              className="text-gray-500 uppercase tracking-wide"
            >
              Price
            </Text>
            <Text className="font-bold text-lg">
              {item.selling_price !== undefined
                ? `${item.selling_price.toFixed(2)} ${currency}`
                : "—"}
            </Text>
          </div>
        </div>
      </div>

      {/* Add to Net Cost Button */}
      <div className="ml-6">
        <Button
          type="button"
          variant="primary"
          size="small"
          onClick={handleAddToNetCost}
          disabled={disabled || item.selling_price === undefined}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add to Net Cost
        </Button>
      </div>
    </div>
  );
};

export default AddonLineItemComponent;
