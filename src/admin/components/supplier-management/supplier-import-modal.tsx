import React, { useState } from "react";
import {
  Button,
  FocusModal,
  Text,
  Heading,
  toast,
} from "@camped-ai/ui";
import { FileIcon, DownloadIcon, UploadIcon, CheckCircleIcon } from "lucide-react";
import { useRefreshSuppliersList } from "../../hooks/supplier-management/use-suppliers-list";

type SupplierImportModalProps = {
  open: boolean;
  onClose: () => void;
};

const SupplierImportModal = ({ open, onClose }: SupplierImportModalProps) => {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [totalRows, setTotalRows] = useState(0);
  const [fullParsedData, setFullParsedData] = useState<any[]>([]);
  const refreshSuppliersList = useRefreshSuppliersList();

  // Define import steps
  const importSteps = [
    { id: 1, title: "Download", description: "Get template" },
    { id: 2, title: "Upload", description: "Upload file" },
    { id: 3, title: "Review", description: "Check results" },
    { id: 4, title: "Import", description: "Process data" },
  ];

  const resetModalState = () => {
    setFile(null);
    setIsUploading(false);
    setUploadResult(null);
    setCurrentStep(1);
    setPreviewData([]);
    setTotalRows(0);
    setFullParsedData([]);
  };

  // Determine current step based on state
  const getCurrentStep = () => {
    if (uploadResult && uploadResult.success) return 4; // Import completed
    if (uploadResult) return 4; // Import in progress/failed
    if (file) return 3; // Review step (file uploaded, ready to import)
    return 1; // Download step
  };

  // Get step status
  const getStepStatus = (stepId: number) => {
    const current = getCurrentStep();

    // If import is successful, mark all steps as completed
    if (uploadResult && uploadResult.success) {
      return 'completed';
    }

    if (stepId < current) return 'completed';
    if (stepId === current) return 'current';
    return 'pending';
  };

  const handleDownloadTemplate = async () => {
    try {
      const response = await fetch('/admin/supplier-management/suppliers/template', {
        method: 'GET',
        credentials: 'include', // Ensure authentication cookies are sent
      });

      if (!response.ok) {
        // Try to get error details from response
        let errorMessage = 'Failed to download template';
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch {
          // If response is not JSON, use default message
        }
        throw new Error(errorMessage);
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'supplier-import-template.xlsx';
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success("Template downloaded successfully");
    } catch (error) {
      toast.error("Failed to download template");
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
    }
  };

  const handleParseFile = async () => {
    if (!file) return;

    setIsUploading(true);
    try {
      // Parse Excel/CSV file
      const formData = new FormData();
      formData.append('file', file);

      // Parse the file to extract supplier data for preview
      const parseResponse = await fetch('/admin/supplier-management/suppliers/parse', {
        method: 'POST',
        body: formData,
      });

      if (!parseResponse.ok) {
        let errorData;
        try {
          errorData = await parseResponse.json();
        } catch (parseError) {
          throw new Error(`Failed to parse file: ${parseResponse.statusText} (${parseResponse.status})`);
        }

        const error = {
          message: errorData.message || 'Failed to parse file',
          errors: errorData.errors || [],
          details: errorData.details || `HTTP ${parseResponse.status}`,
          type: errorData.type || 'parse_error'
        };
        throw error;
      }

      const parsedData = await parseResponse.json();

      if (!parsedData.success) {
        throw new Error(parsedData.message || 'Failed to parse file');
      }

      if (!parsedData.suppliers || parsedData.suppliers.length === 0) {
        throw new Error('No valid supplier data found in the file');
      }

      // Store full data and preview data (first 10 rows) for review
      setFullParsedData(parsedData.suppliers);
      setPreviewData(parsedData.suppliers.slice(0, 10));
      setTotalRows(parsedData.suppliers.length);

      // Stop here - user will review and then click "Proceed with Import"
      setIsUploading(false);

    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to parse file');
      setIsUploading(false);
    }
  };

  const handleImport = async () => {
    if (previewData.length === 0) return;

    setIsUploading(true);
    try {
      // Import the parsed supplier data
      const importResponse = await fetch('/admin/supplier-management/suppliers/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          suppliers: fullParsedData
        }),
      });

      if (!importResponse.ok) {
        let errorData;
        try {
          errorData = await importResponse.json();
        } catch (parseError) {
          throw new Error(`Import failed: ${importResponse.statusText} (${importResponse.status})`);
        }

        // Create detailed error object
        const error = {
          message: errorData.message || `Import failed: ${importResponse.statusText}`,
          errors: errorData.errors || [],
          details: errorData.details || `HTTP ${importResponse.status}`,
          type: errorData.type || 'import_error'
        };

        throw error;
      }

      const result = await importResponse.json();

      setUploadResult({
        success: true,
        message: result.message || "Import completed successfully",
        imported: result.imported || 0,
        errors: result.errors || []
      });

      const contactsMessage = result.contactsCount ? ` with ${result.contactsCount} contacts` : '';
      toast.success(`Successfully imported ${result.imported || 0} suppliers${contactsMessage}`);

      // Refresh the suppliers list to show updated data
      refreshSuppliersList();
    } catch (error) {

      let errorMessage = "An unknown error occurred";
      let errorDetails = [];

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else if (error && typeof error === 'object') {
        // Try to extract error information from response
        const errorObj = error as any;
        if (errorObj.message) {
          errorMessage = errorObj.message;
        }
        if (errorObj.details) {
          errorDetails.push(errorObj.details);
        }
        if (errorObj.errors && Array.isArray(errorObj.errors)) {
          errorDetails = errorObj.errors;
        }

        // Special handling for duplicate errors
        if (errorObj.type === 'duplicate_error') {
          errorMessage = errorObj.message || 'Duplicate suppliers found';
          if (errorObj.errors && Array.isArray(errorObj.errors)) {
            errorDetails = errorObj.errors.map((err: any) =>
              err.message || err.name || err
            );
          }
        }
      }

      toast.error(errorMessage);
      setUploadResult({
        success: false,
        message: errorMessage,
        imported: 0,
        errors: errorDetails.length > 0 ? errorDetails : [errorMessage]
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile) {
      setFile(droppedFile);
    }
  };

  return (
    <FocusModal
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          resetModalState();
          onClose();
        }
      }}
    >
      <FocusModal.Content className="flex flex-col h-full max-h-[98vh] bg-gray-50 dark:bg-gray-900">
        <FocusModal.Header className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center w-full py-4 px-6">
            <div className="flex items-center gap-3">
              <Heading level="h2" className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Import Suppliers
              </Heading>
            </div>

            {/* Progress Indicator */}
            <div className="px-6 py-2 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {importSteps.map((step, index) => {
                    const status = getStepStatus(step.id);
                    const isCompleted = status === 'completed';
                    const isCurrent = status === 'current';

                    return (
                      <React.Fragment key={step.id}>
                        <div className="flex items-center">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                            isCompleted
                              ? "bg-green-600 dark:bg-green-500 text-white"
                              : isCurrent
                              ? "bg-blue-600 dark:bg-blue-500 text-white"
                              : "bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400"
                          }`}>
                            {isCompleted ? "✓" : step.id}
                          </div>
                          <span className={`ml-2 text-sm font-medium ${
                            isCompleted
                              ? "text-green-600 dark:text-green-400"
                              : isCurrent
                              ? "text-blue-600 dark:text-blue-400"
                              : "text-gray-500 dark:text-gray-400"
                          }`}>
                            {step.id === 4 && uploadResult && uploadResult.success ? "Complete" : step.title}
                          </span>
                        </div>
                        {index < importSteps.length - 1 && (
                          <div className={`w-8 h-0.5 ${
                            isCompleted ? "bg-green-300 dark:bg-green-600" : "bg-gray-300 dark:bg-gray-600"
                          }`}></div>
                        )}
                      </React.Fragment>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </FocusModal.Header>

        <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
          <div className="flex-grow overflow-y-auto p-6 space-y-6">
            


            {/* Data Preview Section */}
            {previewData.length > 0 && !uploadResult && (
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm mb-6">
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <Heading level="h3" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                        Data Preview
                      </Heading>
                      <Text className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Showing first 10 rows of {totalRows} total suppliers with all available columns
                      </Text>
                    </div>
                    <div className="bg-blue-100 dark:bg-blue-900/20 px-3 py-1 rounded-full">
                      <Text className="text-sm font-medium text-blue-800 dark:text-blue-200">
                        {totalRows} suppliers found
                      </Text>
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider sticky left-0 bg-gray-50 dark:bg-gray-800 z-10">
                            #
                          </th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider sticky left-8 bg-gray-50 dark:bg-gray-800 z-10">
                            Name
                          </th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Type
                          </th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Website
                          </th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Status
                          </th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Preference
                          </th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Timezone
                          </th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Languages
                          </th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Payment Method
                          </th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Payout Terms
                          </th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Tax ID
                          </th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Currency
                          </th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Bank Details
                          </th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Categories
                          </th>
                          <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Address
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                        {previewData.map((supplier, index) => (
                          <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                            {/* Row Number - Sticky */}
                            <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 sticky left-0 bg-white dark:bg-gray-900 z-10">
                              {index + 1}
                            </td>

                            {/* Name - Sticky */}
                            <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100 sticky left-8 bg-white dark:bg-gray-900 z-10 min-w-[150px]">
                              {supplier.name || '-'}
                            </td>

                            {/* Supplier Type */}
                            <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 min-w-[100px]">
                              {supplier.supplier_type || '-'}
                            </td>

                            {/* Website */}
                            <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 min-w-[150px]">
                              {supplier.website ? (
                                <a
                                  href={supplier.website}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 dark:text-blue-400 hover:underline"
                                  title={supplier.website}
                                >
                                  {supplier.website.length > 25 ? `${supplier.website.substring(0, 25)}...` : supplier.website}
                                </a>
                              ) : '-'}
                            </td>

                            {/* Status */}
                            <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 min-w-[100px]">
                              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                                supplier.status === 'active' || supplier.status === 'Active'
                                  ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200'
                                  : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                              }`}>
                                {supplier.status || 'pending'}
                              </span>
                            </td>

                            {/* Preference */}
                            <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 min-w-[100px]">
                              {supplier.preference ? (
                                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                                  supplier.preference === 'Preferred'
                                    ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200'
                                    : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                                }`}>
                                  {supplier.preference}
                                </span>
                              ) : '-'}
                            </td>

                            {/* Timezone */}
                            <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 min-w-[120px]">
                              {supplier.timezone || '-'}
                            </td>

                            {/* Languages */}
                            <td className="px-3 py-2 text-sm text-gray-600 dark:text-gray-400 min-w-[120px]">
                              {supplier.language_preference ? (
                                Array.isArray(supplier.language_preference)
                                  ? supplier.language_preference.join(', ')
                                  : supplier.language_preference
                              ) : '-'}
                            </td>

                            {/* Payment Method */}
                            <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 min-w-[120px]">
                              {supplier.payment_method || '-'}
                            </td>

                            {/* Payout Terms */}
                            <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 min-w-[100px]">
                              {supplier.payout_terms || '-'}
                            </td>

                            {/* Tax ID */}
                            <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 min-w-[120px]">
                              {supplier.tax_id || '-'}
                            </td>

                            {/* Currency */}
                            <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 min-w-[80px]">
                              {supplier.default_currency || '-'}
                            </td>

                            {/* Bank Details */}
                            <td className="px-3 py-2 text-sm text-gray-600 dark:text-gray-400 min-w-[150px] max-w-[200px]">
                              <div className="truncate" title={supplier.bank_account_details}>
                                {supplier.bank_account_details || '-'}
                              </div>
                            </td>

                            {/* Categories */}
                            <td className="px-3 py-2 text-sm text-gray-600 dark:text-gray-400 min-w-[120px] max-w-[150px]">
                              <div className="truncate" title={supplier.categories}>
                                {supplier.categories || '-'}
                              </div>
                            </td>

                            {/* Address */}
                            <td className="px-3 py-2 text-sm text-gray-600 dark:text-gray-400 min-w-[200px] max-w-[250px]">
                              <div className="truncate" title={supplier.address}>
                                {supplier.address || '-'}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {totalRows > 10 && (
                    <div className="mt-4 text-center">
                      <Text className="text-sm text-gray-500 dark:text-gray-400">
                        ... and {totalRows - 10} more suppliers will be imported
                      </Text>
                    </div>
                  )}
                </div>

                <div className="px-6 py-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 rounded-b-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <Text className="text-sm text-gray-600 dark:text-gray-400">
                        Review the data above and click "Proceed with Import" to continue
                      </Text>
                      <Text className="text-xs text-amber-600 dark:text-amber-400 mt-1">
                        ⚠️ Import will be rejected if any supplier names already exist in the database
                      </Text>
                    </div>
                    <div className="flex gap-3">
                      <Button
                        variant="secondary"
                        onClick={() => {
                          setFile(null);
                          setPreviewData([]);
                          setTotalRows(0);
                        }}
                        className="px-4 py-2"
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="primary"
                        onClick={handleImport}
                        disabled={isUploading}
                        className="px-4 py-2 bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white"
                      >
                        {isUploading ? 'Importing...' : 'Proceed with Import'}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {!uploadResult && previewData.length === 0 ? (
              <>
                {/* Step 1: Download Template */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="flex items-start gap-3 p-6">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">1</span>
                    </div>
                    <div className="flex-1">
                      <Heading level="h3" className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                        Download Import Template
                      </Heading>
                      <Text className="text-gray-600 dark:text-gray-400 mb-4">
                        Get the multi-sheet Excel template with pre-configured fields for suppliers.
                        The template includes sample data and reference sheets to help you format your data correctly.
                      </Text>
                      <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md border border-blue-200 dark:border-blue-700 mb-3">
                        <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Template includes 4 sheets:</Text>
                        <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                          <li>• <strong className="text-gray-800 dark:text-gray-200">Suppliers Sheet:</strong> Basic info, business details, single address field (IDs auto-generated)</li>
                          <li>• <strong className="text-gray-800 dark:text-gray-200">Contacts Sheet:</strong> Contact info linked by supplier name (unlimited contacts per supplier)</li>
                          <li>• <strong className="text-gray-800 dark:text-gray-200">Category References:</strong> Available service categories for reference</li>
                          <li>• <strong className="text-gray-800 dark:text-gray-200">Language References:</strong> Available languages for reference</li>
                          <li>• <strong className="text-gray-800 dark:text-gray-200">Category References:</strong> Available supplier categories fetched from your system</li>
                          <li>• <strong className="text-gray-800 dark:text-gray-200">Smart Import:</strong> Creates new suppliers or updates existing ones based on name/handle</li>
                          <li>• <strong className="text-gray-800 dark:text-gray-200">Live Updates:</strong> Changes appear immediately in the suppliers directory after import</li>
                        </ul>
                      </div>

                      <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-md border border-green-200 dark:border-green-700 mb-3">
                        <Text className="text-sm font-medium text-green-700 dark:text-green-300 mb-1">How it works:</Text>
                        <ul className="text-sm text-green-600 dark:text-green-400 space-y-1">
                          <li>• <strong className="text-green-800 dark:text-green-200">Download:</strong> Get template with current business types and categories</li>
                          <li>• <strong className="text-green-800 dark:text-green-200">Fill Data:</strong> Add suppliers and contacts using dropdown validation</li>
                          <li>• <strong className="text-green-800 dark:text-green-200">Import:</strong> Upload file to create new or update existing suppliers</li>
                          <li>• <strong className="text-green-800 dark:text-green-200">View Results:</strong> Check the suppliers directory for your imported data</li>
                        </ul>
                      </div>
                      <Button
                        variant="secondary"
                        onClick={handleDownloadTemplate}
                        className="flex items-center gap-2 bg-blue-600 dark:bg-blue-500 text-white hover:bg-blue-700 dark:hover:bg-blue-600"
                      >
                        <DownloadIcon className="w-4 h-4" />
                        Download Excel Template
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Step 2: Upload File */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="flex items-start gap-3 p-6">
                    <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                      <span className="text-green-600 dark:text-green-400 font-semibold text-sm">2</span>
                    </div>
                    <div className="flex-1">
                      <Heading level="h3" className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                        Upload Your Data File
                      </Heading>
                      <Text className="text-gray-600 dark:text-gray-400 mb-4">
                        Upload the completed Excel file with your supplier data.
                      </Text>

                      {/* File Upload Area */}
                      <div
                        className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center hover:border-gray-400 dark:hover:border-gray-500 transition-colors"
                        onDragOver={handleDragOver}
                        onDrop={handleDrop}
                      >
                        <UploadIcon className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                        <Text className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                          Click to upload or drag and drop
                        </Text>
                        <Text className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                          Excel (.xlsx, .xls) and CSV files • Maximum 5MB
                        </Text>
                        <input
                          type="file"
                          accept=".xlsx,.xls,.csv"
                          onChange={handleFileSelect}
                          className="hidden"
                          id="file-upload"
                        />
                        <label
                          htmlFor="file-upload"
                          className="inline-flex items-center px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 cursor-pointer"
                        >
                          Select a file to import
                        </label>
                      </div>

                      {file && (
                        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md border border-gray-200 dark:border-gray-600">
                          <div className="flex items-center gap-3">
                            <FileIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                            <div className="flex-1">
                              <Text className="font-medium text-gray-900 dark:text-gray-100">{file.name}</Text>
                              <Text className="text-sm text-gray-500 dark:text-gray-400">
                                {(file.size / 1024 / 1024).toFixed(2)} MB
                              </Text>
                            </div>
                            <Button
                              variant="secondary"
                              size="small"
                              onClick={() => setFile(null)}
                              className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"
                            >
                              Remove
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </>
            ) : uploadResult ? (
              /* Success/Error State */
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm p-6">
                <div className="text-center">
                  {uploadResult.success ? (
                    <>
                      <CheckCircleIcon className="w-16 h-16 text-green-600 dark:text-green-400 mx-auto mb-4" />
                      <Heading level="h3" className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                        Import Completed Successfully!
                      </Heading>
                      <Text className="text-gray-600 dark:text-gray-400 mb-4">
                        Your supplier data has been imported successfully.
                      </Text>
                      <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-700">
                        <Text className="text-green-800 dark:text-green-200 font-medium">
                          {uploadResult.imported} suppliers imported successfully
                        </Text>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-red-600 dark:text-red-400 text-2xl">✕</span>
                      </div>
                      <Heading level="h3" className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                        Import Failed
                      </Heading>
                      <Text className="text-gray-600 dark:text-gray-400 mb-4">
                        There was an error importing your supplier data.
                      </Text>
                      <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-700">
                        <Text className="text-red-800 dark:text-red-200 font-medium">
                          {uploadResult.message}
                        </Text>
                        {uploadResult.errors && uploadResult.errors.length > 0 && (
                          <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                            <ul className="list-disc list-inside">
                              {uploadResult.errors.slice(0, 5).map((error: string, index: number) => (
                                <li key={index}>{error}</li>
                              ))}
                              {uploadResult.errors.length > 5 && (
                                <li>... and {uploadResult.errors.length - 5} more errors</li>
                              )}
                            </ul>
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </div>
              </div>
            ) : (
              /* This case handles when previewData.length > 0 but uploadResult is null */
              /* The preview section above should be showing, so this shouldn't render */
              <div></div>
            )}
          </div>

          {/* Footer */}
          <div className="flex-shrink-0 py-6 px-8 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {uploadResult
                  ? (uploadResult.success ? "Import completed successfully" : "Import failed")
                  : (file ? "Ready to import file" : "Select a file to import")
                }
              </div>
              <div className="flex gap-4">
                <Button
                  variant="secondary"
                  onClick={() => {
                    resetModalState();
                    onClose();
                  }}
                  className="px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600 font-medium"
                >
                  {uploadResult ? "Close" : "Cancel"}
                </Button>
                {!uploadResult && (
                  <Button
                    variant="primary"
                    onClick={handleParseFile}
                    disabled={!file || isUploading}
                    className="flex items-center gap-3 px-6 py-3 bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white shadow-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isUploading ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Parsing...
                      </>
                    ) : (
                      <>
                        <UploadIcon className="w-5 h-5" />
                        {previewData.length > 0 ? "Parse Again" : "Parse & Preview"}
                      </>
                    )}
                  </Button>
                )}
                {uploadResult && !uploadResult.success && (
                  <Button
                    variant="primary"
                    onClick={() => {
                      setUploadResult(null);
                      setFile(null);
                    }}
                    className="flex items-center gap-3 px-6 py-3 bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white shadow-lg font-medium"
                  >
                    Try Again
                  </Button>
                )}
              </div>
            </div>
          </div>
        </FocusModal.Body>
      </FocusModal.Content>
    </FocusModal>
  );
};

export default SupplierImportModal;
