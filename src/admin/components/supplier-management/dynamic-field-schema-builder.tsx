import React, { useState, useEffect } from "react";
import {
  Button,
  Input,
  Label,
  Select,
  Textarea,
  Badge,
  Text,
  FocusModal,
  Heading,
} from "@camped-ai/ui";
import { PlusMini, Trash, InformationCircleSolid } from "@camped-ai/icons";
import {
  X,
  GripVertical,
  Edit,
  Eye,
  ChevronDown,
  ChevronUp,
  Copy,
} from "lucide-react";

export interface DynamicFieldSchema {
  label: string;
  key: string;
  type:
    | "text"
    | "number"
    | "dropdown"
    | "multi-select"
    | "date"
    | "boolean"
    | "number-range"
    | "hotels"
    | "destinations"
    | "addons";
  options?: string[];
  required: boolean;
  used_in_filtering?: boolean;
  used_in_supplier_offering?: boolean;
  used_in_product?: boolean;
  locked_in_offerings?: boolean;
  order?: number; // Optional field ordering for product name generation
  field_context?: "supplier" | "customer"; // Context filtering for fields
}

interface DynamicFieldSchemaBuilderProps {
  value: DynamicFieldSchema[];
  onChange: (fields: DynamicFieldSchema[]) => void;
  error?: string;
  disabled?: boolean;
}

interface FieldFormData {
  label: string;
  key: string;
  type: DynamicFieldSchema["type"];
  options: string[];
  required: boolean;
  used_in_filtering: boolean;
  used_in_supplier_offering: boolean;
  used_in_product: boolean;
  locked_in_offerings: boolean;
  order?: number; // Optional field ordering for product name generation
  field_context?: "supplier" | "customer"; // Context filtering for fields
}

const FIELD_TYPES = [
  {
    value: "text",
    label: "Text",
    description: "Single line text input",
    icon: "📝",
    example: "Hotel Name, Location",
  },
  {
    value: "number",
    label: "Number",
    description: "Numeric input with validation",
    icon: "🔢",
    example: "Price, Capacity, Rating",
  },
  {
    value: "dropdown",
    label: "Dropdown",
    description: "Single selection from predefined options",
    icon: "📋",
    example: "Difficulty Level, Category Type",
  },
  {
    value: "multi-select",
    label: "Multi-select",
    description: "Multiple selections from predefined options",
    icon: "☑️",
    example: "Amenities, Features, Tags",
  },
  {
    value: "date",
    label: "Date",
    description: "Date picker input",
    icon: "📅",
    example: "Available From, Expiry Date",
  },
  {
    value: "boolean",
    label: "Yes/No",
    description: "True/false checkbox",
    icon: "✅",
    example: "Pet Friendly, WiFi Available",
  },
  {
    value: "number-range",
    label: "Number Range",
    description: "Min/max numeric range",
    icon: "📊",
    example: "Age Range, Price Range",
  },
  {
    value: "hotels",
    label: "Hotels",
    description: "Multi-select from available hotels",
    icon: "🏨",
    example: "Available Hotels, Partner Hotels",
  },
  {
    value: "destinations",
    label: "Destinations",
    description: "Multi-select from available destinations",
    icon: "🌍",
    example: "Available Destinations, Target Locations",
  },
  {
    value: "addons",
    label: "Addons",
    description: "Multi-select from available product services",
    icon: "🎯",
    example: "Available Add-ons, Related Services",
  },
] as const;

// Common field templates for quick setup
const FIELD_TEMPLATES = [
  {
    name: "Age Range",
    icon: "👥",
    description: "Minimum and maximum age requirements",
    field: {
      label: "Age Range",
      key: "age_range",
      type: "number-range" as const,
      required: false,
      used_in_filtering: true,
      used_in_supplier_offering: true,
      used_in_product: true,
      locked_in_offerings: false,
      field_context: "supplier" as const,
    },
  },
  {
    name: "Duration",
    icon: "⏱️",
    description: "Activity or service duration",
    field: {
      label: "Duration (hours)",
      key: "duration_hours",
      type: "number" as const,
      required: false,
      used_in_filtering: true,
      used_in_supplier_offering: true,
      used_in_product: true,
      locked_in_offerings: true,
    },
  },
  {
    name: "Difficulty Level",
    icon: "🎯",
    description: "Skill level required",
    field: {
      label: "Difficulty Level",
      key: "difficulty_level",
      type: "dropdown" as const,
      options: ["Beginner", "Intermediate", "Advanced", "Expert"],
      required: false,
      used_in_filtering: true,
      used_in_supplier_offering: true,
      used_in_product: true,
      locked_in_offerings: false,
    },
  },
  {
    name: "Amenities",
    icon: "🏨",
    description: "Available facilities and services",
    field: {
      label: "Amenities",
      key: "amenities",
      type: "multi-select" as const,
      options: ["WiFi", "Parking", "Pool", "Gym", "Restaurant", "Spa"],
      required: false,
      used_in_filtering: true,
      used_in_supplier_offering: true,
      used_in_product: true,
      locked_in_offerings: false,
    },
  },
  {
    name: "Days Available",
    icon: "📅",
    description: "Days of the week when service is available",
    field: {
      label: "Days Available",
      key: "days_available",
      type: "multi-select" as const,
      options: [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
      ],
      required: false,
      used_in_filtering: true,
      used_in_supplier_offering: true,
      used_in_product: true,
      locked_in_offerings: false,
      field_context: "supplier" as const,
    },
  },
  {
    name: "Days Booked",
    icon: "📊",
    description: "Number of days the service is booked for (affects pricing)",
    field: {
      label: "Days Booked",
      key: "days_booked",
      type: "number" as const,
      required: false,
      used_in_filtering: true,
      used_in_supplier_offering: true,
      used_in_product: true,
      locked_in_offerings: false,
      field_context: "customer" as const,
    },
  },

  {
    name: "Pet Friendly",
    icon: "🐕",
    description: "Whether pets are allowed",
    field: {
      label: "Pet Friendly",
      key: "pet_friendly",
      type: "boolean" as const,
      required: false,
      used_in_filtering: true,
      used_in_supplier_offering: true,
      used_in_product: true,
      locked_in_offerings: true,
    },
  },
  {
    name: "Price Range",
    icon: "💰",
    description: "Supplier's price range for this offering",
    field: {
      label: "Price Range (CHF)",
      key: "price_range",
      type: "number-range" as const,
      required: false,
      used_in_filtering: false,
      used_in_supplier_offering: true,
      used_in_product: false,
      locked_in_offerings: false,
    },
  },
  {
    name: "Capacity",
    icon: "👥",
    description: "Maximum number of people/guests",
    field: {
      label: "Maximum Capacity",
      key: "max_capacity",
      type: "number" as const,
      required: false,
      used_in_filtering: true,
      used_in_supplier_offering: true,
      used_in_product: true,
      locked_in_offerings: false,
    },
  },
  {
    name: "Seasonal Availability",
    icon: "🗓️",
    description: "When this offering is available",
    field: {
      label: "Season",
      key: "season",
      type: "dropdown" as const,
      options: [
        "Year Round",
        "Winter Only",
        "Summer Only",
        "Spring/Summer",
        "Fall/Winter",
      ],
      required: false,
      used_in_filtering: true,
      used_in_supplier_offering: true,
      used_in_product: true,
      locked_in_offerings: false,
    },
  },
] as const;

const DynamicFieldSchemaBuilder: React.FC<DynamicFieldSchemaBuilderProps> = ({
  value,
  onChange,
  error,
  disabled = false,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [formData, setFormData] = useState<FieldFormData>({
    label: "",
    key: "",
    type: "text",
    options: [],
    required: false,
    used_in_filtering: false,
    used_in_supplier_offering: false,
    used_in_product: false,
    locked_in_offerings: false,
    order: undefined,
    field_context: "supplier", // Default to supplier context
  });
  const [optionInput, setOptionInput] = useState("");
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [showPreview, setShowPreview] = useState(false);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [showTemplates, setShowTemplates] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  // Auto-generate key from label
  const generateKey = (label: string): string => {
    return label
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, "")
      .replace(/\s+/g, "_")
      .replace(/^_+|_+$/g, "");
  };

  // Real-time validation
  const validateField = (
    field: Partial<FieldFormData>
  ): Record<string, string> => {
    const errors: Record<string, string> = {};

    if (!field.label?.trim()) {
      errors.label = "Field label is required";
    }

    if (!field.key?.trim()) {
      errors.key = "Field key is required";
    } else if (!/^[a-z][a-z0-9_]*$/.test(field.key)) {
      errors.key =
        "Key must start with a letter and contain only lowercase letters, numbers, and underscores";
    } else if (
      value.some((f, i) => f.key === field.key && i !== editingIndex)
    ) {
      errors.key = "This key already exists";
    }

    if (
      (field.type === "dropdown" || field.type === "multi-select") &&
      (!field.options || field.options.length === 0)
    ) {
      errors.options =
        "At least one option is required for dropdown/multi-select fields";
    }

    return errors;
  };

  const resetForm = () => {
    setFormData({
      label: "",
      key: "",
      type: "text",
      options: [],
      required: false,
      used_in_filtering: false,
      used_in_supplier_offering: false,
      used_in_product: false,
      locked_in_offerings: false,
      order: undefined,
      field_context: "supplier", // Default to supplier context
    });
    setOptionInput("");
    setFormErrors({});
    setShowPreview(false);
    setSelectedTemplate(null);
  };

  // Real-time validation effect
  useEffect(() => {
    if (isModalOpen) {
      const errors = validateField(formData);
      setFormErrors(errors);
    }
  }, [formData, isModalOpen, value, editingIndex]);

  // Drag and drop handlers
  const handleDragStart = (index: number) => {
    setDraggedIndex(index);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    if (draggedIndex === null || draggedIndex === dropIndex) return;

    const newFields = [...value];
    const draggedField = newFields[draggedIndex];
    newFields.splice(draggedIndex, 1);
    newFields.splice(dropIndex, 0, draggedField);

    onChange(newFields);
    setDraggedIndex(null);
  };

  // Template handlers
  const applyTemplate = (template: (typeof FIELD_TEMPLATES)[number]) => {
    setFormData({
      ...template.field,
      options: (template.field as any).options || [],
    });
    setSelectedTemplate(template.name);
    setShowTemplates(false);
  };

  const openCreateModal = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    resetForm();
    setEditingIndex(null);
    setIsModalOpen(true);
  };

  const openEditModal = (index: number, e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    const field = value[index];
    setFormData({
      label: field.label,
      key: field.key,
      type: field.type,
      options: field.options || [],
      required: field.required,
      used_in_filtering: field.used_in_filtering || false,
      used_in_supplier_offering: field.used_in_supplier_offering || false,
      used_in_product: field.used_in_product || false,
      locked_in_offerings: field.locked_in_offerings || false,
      order: field.order,
      field_context: field.field_context || "supplier", // Default to supplier if not set
    });
    setEditingIndex(index);
    setIsModalOpen(true);
  };

  const closeModal = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    setIsModalOpen(false);
    setEditingIndex(null);
    resetForm();
  };

  const generateKeyFromLabel = (label: string) => {
    return label
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, "")
      .replace(/\s+/g, "_")
      .replace(/^_+|_+$/g, "");
  };

  const handleLabelChange = (label: string) => {
    setFormData((prev) => ({
      ...prev,
      label,
      // Auto-generate key only if it's empty or matches the previous auto-generated key
      key:
        !prev.key || prev.key === generateKey(prev.label)
          ? generateKey(label)
          : prev.key,
    }));
  };

  const handleKeyChange = (key: string) => {
    setFormData((prev) => ({ ...prev, key }));
  };

  const handleTypeChange = (type: DynamicFieldSchema["type"]) => {
    setFormData((prev) => ({
      ...prev,
      type,
      options:
        type === "dropdown" || type === "multi-select" ? prev.options : [],
    }));
  };

  const addOption = (e?: React.KeyboardEvent) => {
    if (e && e.key !== "Enter") return;

    const trimmedOption = optionInput.trim();
    if (trimmedOption && !formData.options.includes(trimmedOption)) {
      setFormData((prev) => ({
        ...prev,
        options: [...prev.options, trimmedOption],
      }));
      setOptionInput("");
    }
  };

  const removeOption = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index),
    }));
  };

  const moveOption = (fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= formData.options.length) return;

    setFormData((prev) => {
      const newOptions = [...prev.options];
      const [movedOption] = newOptions.splice(fromIndex, 1);
      newOptions.splice(toIndex, 0, movedOption);
      return { ...prev, options: newOptions };
    });
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.label.trim()) {
      errors.label = "Label is required";
    }

    if (!formData.key.trim()) {
      errors.key = "Key is required";
    } else if (!/^[a-z_]+$/.test(formData.key)) {
      errors.key =
        "Key must be in snake_case format (lowercase letters and underscores only)";
    } else {
      // Check for duplicate keys (excluding current field if editing)
      const existingKeys = value
        .filter((_, index) => index !== editingIndex)
        .map((field) => field.key);
      if (existingKeys.includes(formData.key)) {
        errors.key = "Key must be unique";
      }
    }

    if (
      (formData.type === "dropdown" || formData.type === "multi-select") &&
      formData.options.length === 0
    ) {
      errors.options =
        "At least one option is required for dropdown and multi-select fields";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!validateForm()) {
      return;
    }

    const newField: DynamicFieldSchema = {
      label: formData.label,
      key: formData.key,
      type: formData.type,
      required: formData.required,
      used_in_filtering: formData.used_in_filtering,
      used_in_supplier_offering: formData.used_in_supplier_offering,
      used_in_product: formData.used_in_product,
      locked_in_offerings: formData.locked_in_offerings,
      field_context: formData.field_context,
    };

    // Only include order if it's defined
    if (formData.order !== undefined) {
      newField.order = formData.order;
    }

    if (formData.type === "dropdown" || formData.type === "multi-select") {
      newField.options = formData.options;
    }

    if (editingIndex !== null) {
      const updatedFields = [...value];
      updatedFields[editingIndex] = newField;
      onChange(updatedFields);
    } else {
      onChange([...value, newField]);
    }

    closeModal();
  };

  const removeField = (index: number, e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    onChange(value.filter((_, i) => i !== index));
  };

  const moveField = (fromIndex: number, toIndex: number) => {
    const newFields = [...value];
    const [movedField] = newFields.splice(fromIndex, 1);
    newFields.splice(toIndex, 0, movedField);
    onChange(newFields);
  };

  // Field preview component
  const FieldPreview: React.FC<{ field: FieldFormData }> = ({ field }) => {
    const renderPreviewField = () => {
      switch (field.type) {
        case "text":
          return (
            <Input
              placeholder={`Enter ${field.label.toLowerCase()}`}
              disabled
              className="opacity-60"
            />
          );
        case "number":
          return (
            <Input
              type="number"
              placeholder={`Enter ${field.label.toLowerCase()}`}
              disabled
              className="opacity-60"
            />
          );
        case "dropdown":
          return (
            <Select disabled>
              <option value="">Select {field.label.toLowerCase()}</option>
              {field.options.map((option, i) => (
                <option key={i} value={option}>
                  {option}
                </option>
              ))}
            </Select>
          );
        case "multi-select":
          return (
            <div className="border border-gray-300 rounded-md p-2 bg-gray-50">
              <Text size="small" className="text-gray-500">
                Multiple selection: {field.options.join(", ")}
              </Text>
            </div>
          );
        case "date":
          return <Input type="date" disabled className="opacity-60" />;
        case "boolean":
          return (
            <div className="flex items-center gap-2">
              <input type="checkbox" disabled className="opacity-60" />
              <Label className="opacity-60">{field.label}</Label>
            </div>
          );
        case "number-range":
          return (
            <div className="flex items-center gap-2">
              <Input
                type="number"
                placeholder="Min"
                disabled
                className="opacity-60 flex-1"
              />
              <Text className="opacity-60">to</Text>
              <Input
                type="number"
                placeholder="Max"
                disabled
                className="opacity-60 flex-1"
              />
            </div>
          );
        default:
          return null;
      }
    };

    return (
      <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
        <div className="flex items-center gap-2 mb-3">
          <Eye className="h-4 w-4 text-gray-500" />
          <Text weight="medium">Preview</Text>
        </div>
        <div className="space-y-2">
          <Label>
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          {renderPreviewField()}
          {field.used_in_filtering && (
            <Badge variant="secondary" size="small">
              Used in filtering
            </Badge>
          )}
          {field.used_in_supplier_offering && (
            <Badge variant="green" size="small">
              Used in supplier offering
            </Badge>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Text size="small" className="text-ui-fg-subtle">
            Define custom fields that will appear when creating
            products/services in this category
          </Text>
        </div>
        <div className="flex items-center gap-2">
          <Button
            type="button"
            size="small"
            variant="secondary"
            onClick={() => setShowTemplates(!showTemplates)}
            disabled={disabled}
          >
            <Copy className="h-4 w-4" />
            Templates
          </Button>
          <Button
            type="button"
            size="small"
            variant="secondary"
            onClick={openCreateModal}
            disabled={disabled}
          >
            <PlusMini />
            Add Field
          </Button>
        </div>
      </div>

      {/* Field Templates */}
      {showTemplates && (
        <div className="p-4 border border-gray-200 rounded-lg bg-white">
          <div className="flex items-center gap-2 mb-3">
            <Copy className="h-4 w-4 text-gray-500" />
            <Text weight="medium">Quick Templates</Text>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {FIELD_TEMPLATES.map((template) => (
              <button
                key={template.name}
                onClick={() => {
                  applyTemplate(template);
                  setIsModalOpen(true);
                }}
                className="text-left p-3 border border-gray-200 rounded-lg hover:border-gray-300 hover:bg-gray-50 transition-colors"
                disabled={disabled}
              >
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-lg">{template.icon}</span>
                  <Text weight="medium" size="small">
                    {template.name}
                  </Text>
                </div>
                <Text size="xsmall" className="text-gray-500">
                  {template.description}
                </Text>
              </button>
            ))}
          </div>
        </div>
      )}

      {error && (
        <Text size="small" className="text-red-600">
          {error}
        </Text>
      )}

      {value.length === 0 ? (
        <div className="border-2 border-dashed border-gray-200 rounded-lg p-12 text-center">
          <div className="max-w-md mx-auto">
            <Text className="text-ui-fg-subtle mb-4">
              No custom fields defined yet
            </Text>
            <Text size="small" className="text-ui-fg-muted mb-6">
              Custom fields allow you to collect specific information when
              suppliers create products or services in this category. For
              example, you might want to collect "Minimum Age", "Duration", or
              "Skill Level" for activity-based services.
            </Text>
            <Button type="button" variant="secondary" onClick={openCreateModal}>
              <PlusMini />
              Create Your First Field
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Text size="small" className="text-ui-fg-subtle">
              {value.length} custom field{value.length !== 1 ? "s" : ""} defined
            </Text>
            <div className="flex items-center gap-2 text-xs text-ui-fg-muted">
              <span>Required: {value.filter((f) => f.required).length}</span>
              <span>•</span>
              <span>
                Filterable: {value.filter((f) => f.used_in_filtering).length}
              </span>
              <span>•</span>
              <span>
                Supplier Offering:{" "}
                {value.filter((f) => f.used_in_supplier_offering).length}
              </span>
            </div>
          </div>

          <div className="grid gap-4">
            {value.map((field, index) => (
              <div
                key={index}
                draggable={!disabled}
                onDragStart={() => handleDragStart(index)}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, index)}
                className={`flex items-start gap-4 p-4 border rounded-lg bg-white hover:bg-gray-50 transition-colors ${
                  draggedIndex === index ? "opacity-50" : ""
                } ${!disabled ? "cursor-move" : ""}`}
              >
                <div className="flex flex-col items-center gap-1 flex-shrink-0">
                  <GripVertical className="h-5 w-5 text-gray-400 cursor-move" />
                  <Text size="xsmall" className="text-gray-400">
                    #{index + 1}
                  </Text>
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <Text className="font-medium text-base">{field.label}</Text>
                    <div className="flex items-center gap-1">
                      <span className="text-lg">
                        {FIELD_TYPES.find((t) => t.value === field.type)?.icon}
                      </span>
                      <Badge variant="secondary" size="small">
                        {FIELD_TYPES.find((t) => t.value === field.type)?.label}
                      </Badge>
                    </div>
                    {field.required && (
                      <Badge variant="default" size="small">
                        Required
                      </Badge>
                    )}
                    {field.used_in_filtering && (
                      <Badge variant="blue" size="small">
                        Filterable
                      </Badge>
                    )}
                    {field.used_in_supplier_offering && (
                      <Badge variant="green" size="small">
                        Supplier Offering
                      </Badge>
                    )}
                    {field.used_in_product && (
                      <Badge variant="purple" size="small">
                        Product Form
                      </Badge>
                    )}
                    {field.field_context && (
                      <Badge size="small">
                        <span className="flex items-center gap-1">
                          <span>
                            {field.field_context === "supplier" ? "📦" : "👤"}
                          </span>
                          <span className="capitalize">
                            {field.field_context}
                          </span>
                        </span>
                      </Badge>
                    )}
                    {field.locked_in_offerings && (
                      <Badge size="small">Locked in Offerings</Badge>
                    )}
                  </div>

                  <div className="space-y-1">
                    <Text size="small" className="text-ui-fg-subtle">
                      <span className="font-medium">Key:</span> {field.key}
                    </Text>

                    {field.options && field.options.length > 0 && (
                      <div className="flex items-start gap-2">
                        <Text
                          size="small"
                          className="text-ui-fg-subtle font-medium flex-shrink-0"
                        >
                          Options:
                        </Text>
                        <div className="flex flex-wrap gap-1">
                          {field.options.slice(0, 3).map((option, optIndex) => (
                            <Badge key={optIndex} size="small">
                              {option}
                            </Badge>
                          ))}
                          {field.options.length > 3 && (
                            <Badge size="small">
                              +{field.options.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-1 flex-shrink-0">
                  <Button
                    type="button"
                    size="small"
                    variant="transparent"
                    onClick={() => moveField(index, index - 1)}
                    disabled={disabled || index === 0}
                    className="hover:bg-gray-100"
                    title="Move up"
                  >
                    <ChevronUp className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    size="small"
                    variant="transparent"
                    onClick={() => moveField(index, index + 1)}
                    disabled={disabled || index === value.length - 1}
                    className="hover:bg-gray-100"
                    title="Move down"
                  >
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    size="small"
                    variant="transparent"
                    onClick={(e) => openEditModal(index, e)}
                    className="hover:bg-blue-50 hover:text-blue-600"
                    disabled={disabled}
                    title="Edit field"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    size="small"
                    variant="transparent"
                    onClick={(e) => removeField(index, e)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    disabled={disabled}
                    title="Delete field"
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Field Creation/Edit Modal */}
      <FocusModal open={isModalOpen} onOpenChange={setIsModalOpen}>
        <FocusModal.Content className="max-w-8xl mx-4 max-h-[90vh] flex flex-col">
          <FocusModal.Header className="flex-shrink-0 sticky top-0 z-10 bg-white border-b border-gray-200 px-4 sm:px-6 lg:px-8">
            <Heading level="h3">
              {editingIndex !== null ? "Edit Field" : "Add New Field"}
            </Heading>
            <Text size="small" className="text-ui-fg-subtle mt-1">
              {editingIndex !== null
                ? "Update the field configuration below"
                : "Create a custom field that will appear in the product/service form"}
            </Text>
          </FocusModal.Header>

          <form onSubmit={handleSubmit} className="flex flex-col h-full">
            <FocusModal.Body className="flex-1 overflow-y-auto scroll-smooth px-4 sm:px-6 lg:px-8 py-6 space-y-6 max-h-[calc(90vh-200px)]">
              {/* Basic Field Information */}
              <div className="space-y-4">
                <Heading
                  level="h3"
                  className="text-sm font-medium text-gray-900 sticky top-0 bg-white py-2 border-b border-gray-100"
                >
                  Basic Information
                </Heading>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="field-label">Field Label *</Label>
                    <Input
                      id="field-label"
                      value={formData.label}
                      onChange={(e) => handleLabelChange(e.target.value)}
                      placeholder="e.g., Minimum Age, Duration Type"
                      className={formErrors.label ? "border-red-500" : ""}
                    />
                    {formErrors.label && (
                      <Text size="small" className="text-red-600 mt-1">
                        {formErrors.label}
                      </Text>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="field-key">Field Key *</Label>
                    <Input
                      id="field-key"
                      value={formData.key}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          key: e.target.value,
                        }))
                      }
                      placeholder="e.g., min_age, duration_type"
                      className={formErrors.key ? "border-red-500" : ""}
                    />
                    <Text size="small" className="text-ui-fg-subtle">
                      Used internally. Must be snake_case.
                    </Text>
                    {formErrors.key && (
                      <Text size="small" className="text-red-600 mt-1">
                        {formErrors.key}
                      </Text>
                    )}
                  </div>
                </div>
              </div>

              {/* Field Context Selection */}
              <div className="space-y-4">
                <Heading
                  level="h3"
                  className="text-sm font-medium text-gray-900 sticky top-0 bg-white py-2 border-b border-gray-100"
                >
                  Field Context
                </Heading>
                <div>
                  <Label htmlFor="field-context">Field Context *</Label>
                  <Select
                    value={formData.field_context || "supplier"}
                    onValueChange={(value) =>
                      setFormData((prev) => ({
                        ...prev,
                        field_context: value as "supplier" | "customer",
                      }))
                    }
                  >
                    <Select.Trigger className="w-full">
                      <Select.Value placeholder="Select field context" />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Item value="supplier">
                        <div className="flex flex-col">
                          <span className="font-medium flex items-center gap-2">
                            <span>Supplier Field</span>
                          </span>{" "}
                        </div>
                      </Select.Item>
                      <Select.Item value="customer">
                        <div className="flex flex-col">
                          <span className="font-medium flex items-center gap-2">
                            <span>Customer Field</span>
                          </span>
                        </div>
                      </Select.Item>
                    </Select.Content>
                  </Select>
                  <Text size="small" className="text-ui-fg-subtle mt-1">
                    {formData.field_context === "supplier"
                      ? "📦 This field will appear in supplier management forms (products, services, offerings)"
                      : "👤 This field will appear in customer booking forms for add-ons"}
                  </Text>
                </div>
              </div>

              {/* Field Type Selection */}
              <div className="space-y-4">
                <Heading
                  level="h3"
                  className="text-sm font-medium text-gray-900 sticky top-0 bg-white py-2 border-b border-gray-100"
                >
                  Field Type
                </Heading>
                <div>
                  <div className="flex items-center gap-2 mb-4">
                    <Label htmlFor="field-type">Field Type *</Label>
                    {selectedTemplate && (
                      <Badge size="small">
                        From template: {selectedTemplate}
                      </Badge>
                    )}
                  </div>

                  {/* Enhanced Field Type Selection */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-3">
                    {FIELD_TYPES.filter((type) => {
                      // Hide hotels, destinations, and addons field types when field context is customer
                      if (formData.field_context === "customer") {
                        return (
                          type.value !== "hotels" &&
                          type.value !== "destinations" &&
                          type.value !== "addons"
                        );
                      }
                      return true;
                    }).map((type) => (
                      <button
                        key={type.value}
                        type="button"
                        onClick={() => handleTypeChange(type.value)}
                        className={`text-left p-4 border rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                          formData.type === type.value
                            ? "border-blue-500 bg-blue-50 shadow-sm ring-1 ring-blue-200"
                            : "border-gray-200 hover:border-gray-300 hover:bg-gray-50 hover:shadow-sm"
                        }`}
                      >
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-lg">{type.icon}</span>
                          <Text weight="plus" size="small">
                            {type.label}
                          </Text>
                        </div>
                        <Text
                          size="xsmall"
                          className="text-gray-500 mb-2 leading-relaxed"
                        >
                          {type.description}
                        </Text>
                        <Text size="xsmall" className="text-gray-400 italic">
                          e.g., {type.example}
                        </Text>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Options Configuration */}
              {(formData.type === "dropdown" ||
                formData.type === "multi-select") && (
                <div className="space-y-4">
                  <Heading
                    level="h4"
                    className="text-sm font-medium text-gray-900 sticky top-0 bg-white py-2 border-b border-gray-100"
                  >
                    Options Configuration
                  </Heading>

                  <div className="space-y-4">
                    <div>
                      <Label>Options *</Label>
                      <Text size="small" className="text-ui-fg-subtle mb-3">
                        Add the available options for this{" "}
                        {formData.type === "dropdown"
                          ? "dropdown"
                          : "multi-select"}{" "}
                        field
                      </Text>
                    </div>

                    <div className="space-y-4">
                      <div className="flex gap-2">
                        <Input
                          value={optionInput}
                          onChange={(e) => setOptionInput(e.target.value)}
                          placeholder="Enter option (e.g., Half-day, Full-day)"
                          onKeyDown={(e) => addOption(e)}
                          className="flex-1"
                        />
                        <Button
                          type="button"
                          size="small"
                          onClick={() => addOption()}
                          disabled={
                            !optionInput.trim() ||
                            formData.options.includes(optionInput.trim())
                          }
                        >
                          <PlusMini className="h-4 w-4" />
                          Add
                        </Button>
                      </div>

                      {optionInput.trim() &&
                        formData.options.includes(optionInput.trim()) && (
                          <Text size="small" className="text-amber-600">
                            This option already exists
                          </Text>
                        )}

                      {formData.options.length > 0 && (
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Text size="small" className="text-ui-fg-subtle">
                              {formData.options.length} option
                              {formData.options.length !== 1 ? "s" : ""} added:
                            </Text>
                            <Text size="xsmall" className="text-gray-400">
                              Drag to reorder
                            </Text>
                          </div>
                          <div className="space-y-2 max-h-40 overflow-y-auto scroll-smooth border border-gray-200 rounded-md bg-gray-50/30">
                            {formData.options.map((option, index) => (
                              <div
                                key={index}
                                className="flex items-center gap-2 p-3 border-b border-gray-100 last:border-b-0 hover:bg-white transition-colors"
                              >
                                <GripVertical className="h-4 w-4 text-gray-400 cursor-move" />
                                <Text size="small" className="flex-1">
                                  {option}
                                </Text>
                                <div className="flex items-center gap-1">
                                  <Button
                                    type="button"
                                    size="small"
                                    variant="transparent"
                                    onClick={() => moveOption(index, index - 1)}
                                    disabled={index === 0}
                                    className="hover:bg-gray-200"
                                  >
                                    <ChevronUp className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    type="button"
                                    size="small"
                                    variant="transparent"
                                    onClick={() => moveOption(index, index + 1)}
                                    disabled={
                                      index === formData.options.length - 1
                                    }
                                    className="hover:bg-gray-200"
                                  >
                                    <ChevronDown className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    type="button"
                                    size="small"
                                    variant="transparent"
                                    onClick={() => removeOption(index)}
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            ))}
                          </div>
                          {formData.options.length > 5 && (
                            <div className="text-center py-2 bg-gray-100 border-t border-gray-200 rounded-b-md">
                              <Text size="xsmall" className="text-gray-500">
                                {formData.options.length} options • Scroll to
                                see more
                              </Text>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                    {formErrors.options && (
                      <Text size="small" className="text-red-600 mt-1">
                        {formErrors.options}
                      </Text>
                    )}
                  </div>
                </div>
              )}

              {/* Field Settings */}
              <div className="space-y-4">
                <Heading
                  level="h4"
                  className="text-sm font-medium text-gray-900 sticky top-0 bg-white py-2 border-b border-gray-100"
                >
                  Field Settings
                </Heading>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start gap-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                    <input
                      type="checkbox"
                      id="field-required"
                      checked={formData.required}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          required: e.target.checked,
                        }))
                      }
                      className="rounded border-gray-300 mt-0.5 focus:ring-2 focus:ring-blue-500"
                    />
                    <div className="space-y-1">
                      <Label
                        htmlFor="field-required"
                        className="font-medium cursor-pointer"
                      >
                        Required Field
                      </Label>
                      <Text size="small" className="text-ui-fg-subtle">
                        Users must fill this field when creating
                        products/services
                      </Text>
                    </div>
                  </div>

                  {/* Only show supplier-specific settings when field context is not customer */}
                  {formData.field_context !== "customer" && (
                    <>
                      <div className="flex items-start gap-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                        <input
                          type="checkbox"
                          id="field-filtering"
                          checked={formData.used_in_filtering}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              used_in_filtering: e.target.checked,
                            }))
                          }
                          className="rounded border-gray-300 mt-0.5 focus:ring-2 focus:ring-blue-500"
                        />
                        <div className="space-y-1">
                          <Label
                            htmlFor="field-filtering"
                            className="font-medium cursor-pointer"
                          >
                            Use in Filtering
                          </Label>
                          <Text size="small" className="text-ui-fg-subtle">
                            Allow filtering products/services by this field
                            value
                          </Text>
                        </div>
                      </div>

                      <div className="flex items-start gap-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                        <input
                          type="checkbox"
                          id="field-supplier-offering"
                          checked={formData.used_in_supplier_offering}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              used_in_supplier_offering: e.target.checked,
                            }))
                          }
                          className="rounded border-gray-300 mt-0.5 focus:ring-2 focus:ring-blue-500"
                        />
                        <div className="space-y-1">
                          <Label
                            htmlFor="field-supplier-offering"
                            className="font-medium cursor-pointer"
                          >
                            Use in Supplier Offering
                          </Label>
                          <Text size="small" className="text-ui-fg-subtle">
                            Include this field when creating supplier offerings
                            for this category
                          </Text>
                        </div>
                      </div>

                      <div className="flex items-start gap-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                        <input
                          type="checkbox"
                          id="field-product"
                          checked={formData.used_in_product}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              used_in_product: e.target.checked,
                            }))
                          }
                          className="rounded border-gray-300 mt-0.5 focus:ring-2 focus:ring-blue-500"
                        />
                        <div className="space-y-1">
                          <Label
                            htmlFor="field-product"
                            className="font-medium cursor-pointer"
                          >
                            Use in Product Name
                          </Label>
                          <Text size="small" className="text-ui-fg-subtle">
                            Include this field's value in the auto-generated
                            product/service name. Fields marked here will appear
                            in the product name regardless of whether they're
                            required.
                          </Text>
                        </div>
                      </div>

                      {/* Order Field - Only show if used_in_product is checked */}
                      {/* {formData.used_in_product && (
                        <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
                          <div className="space-y-3">
                            <Label
                              htmlFor="field-order"
                              className="font-medium"
                            >
                              Order in Product Name (Optional)
                            </Label>
                            <Text size="small" className="text-ui-fg-subtle">
                              Control the order of this field in the generated
                              product name. Lower numbers appear first. Leave
                              empty for alphabetical ordering.
                            </Text>
                            <Input
                              id="field-order"
                              type="number"
                              min="1"
                              max="100"
                              value={formData.order || ""}
                              onChange={(e) =>
                                setFormData((prev) => ({
                                  ...prev,
                                  order: e.target.value
                                    ? parseInt(e.target.value)
                                    : undefined,
                                }))
                              }
                              placeholder="e.g., 1, 2, 3..."
                              className="w-32"
                            />
                          </div>
                        </div>
                      )} */}

                      <div className="flex items-start gap-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                        <input
                          type="checkbox"
                          id="field-locked-offerings"
                          checked={formData.locked_in_offerings}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              locked_in_offerings: e.target.checked,
                            }))
                          }
                          className="rounded border-gray-300 mt-0.5 focus:ring-2 focus:ring-blue-500"
                        />
                        <div className="space-y-1">
                          <Label
                            htmlFor="field-locked-offerings"
                            className="font-medium cursor-pointer"
                          >
                            Locked in Offerings
                          </Label>
                          <Text size="small" className="text-ui-fg-subtle">
                            Field value is inherited from product/service and
                            cannot
                          </Text>
                        </div>
                      </div>
                    </>
                  )}

                  {/* Show a message when field context is customer */}
                  {formData.field_context === "customer" && (
                    <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
                      <div className="space-y-2">
                        <Text weight="plus" size="small">
                          Customer Field Settings
                        </Text>
                        <Text size="small" className="text-ui-fg-subtle">
                          This field will be used in customer booking forms.
                          Advanced supplier settings are not applicable.
                        </Text>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Field Preview */}
              {formData.label && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between sticky top-0 bg-white py-2 border-b border-gray-100">
                    <Heading
                      level="h4"
                      className="text-sm font-medium text-gray-900"
                    >
                      Field Preview
                    </Heading>
                    <Button
                      type="button"
                      size="small"
                      variant="secondary"
                      onClick={() => setShowPreview(!showPreview)}
                    >
                      <Eye className="h-4 w-4" />
                      {showPreview ? "Hide" : "Show"} Preview
                    </Button>
                  </div>

                  {showPreview && (
                    <div className="mt-4">
                      <FieldPreview field={formData} />
                    </div>
                  )}
                </div>
              )}
            </FocusModal.Body>

            <FocusModal.Footer className="flex-shrink-0 sticky bottom-0 z-10 bg-white border-t border-gray-200 px-4 sm:px-6 lg:px-8">
              <div className="flex items-center justify-between py-8">
                <div className="flex items-center gap-2">
                  {Object.keys(formErrors).length > 0 && (
                    <div className="flex items-center gap-2 text-red-600">
                      <InformationCircleSolid className="h-4 w-4" />
                      <Text size="small">
                        Please fix {Object.keys(formErrors).length} error
                        {Object.keys(formErrors).length !== 1 ? "s" : ""} above
                      </Text>
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-3">
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={closeModal}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={Object.keys(formErrors).length > 0}
                  >
                    {editingIndex !== null ? "Update Field" : "Add Field"}
                  </Button>
                </div>
              </div>
            </FocusModal.Footer>
          </form>
        </FocusModal.Content>
      </FocusModal>
    </div>
  );
};

export default DynamicFieldSchemaBuilder;
