import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Container,
  Heading,
  Text,
  toast,
  Toaster,
  Input,
  Label,
  Drawer,
  Select,
} from "@camped-ai/ui";
import {
  Save,
  Loader2,
  Calendar,
  Calculator,
  Copy,
  DollarSign,
  Percent,
  TrendingUp,
} from "lucide-react";
// Removed useAdminHotelPricing import - using direct fetch to avoid duplicate API calls
import { useAdminCurrencies } from "../../../hooks/use-admin-currencies";
import { CurrencySelector } from "../../common/currency-selector";
// Import cost-margin calculator utilities
import { calculateTotalFromCostMargin } from "../../../../modules/hotel-management/hotel-pricing/utils/cost-margin-calculator";

import { format } from "date-fns";
import BulkPriceUpdateModal from "./bulk-price-update-modal";
import HotelPricingTableSkeleton from "../../shared/hotel-pricing-table-skeleton";

// Helper function to generate a unique ID
const generateId = () => {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
};

type RoomConfig = {
  id: string;
  title: string;
  handle?: string;
  description?: string;
};

type OccupancyConfig = {
  id: string;
  name: string;
  is_default?: boolean;
};

type MealPlan = {
  id: string;
  name: string;
  is_default?: boolean;
  metadata?: Record<string, any> & {
    applicable_occupancy_types?: string[] | null;
  };
};

type SeasonalPeriod = {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
};

type PricingRow = {
  id: string;
  roomConfigId: string;
  occupancyTypeId: string;
  mealPlanId: string | null; // null for extra beds and cots
  seasonalPeriodId?: string; // null/undefined for base pricing
  prices: {
    mon: number;
    tue: number;
    wed: number;
    thu: number;
    fri: number;
    sat: number;
    sun: number;
  };
  // Default cost and margin fields
  defaultValues: {
    grossCost: number;
    fixedMargin: number;
    marginPercentage: number;
    total: number;
  };
  // Weekday-specific cost and margin fields
  weekdayValues: {
    mon: {
      grossCost: number;
      fixedMargin: number;
      marginPercentage: number;
      total: number;
    };
    tue: {
      grossCost: number;
      fixedMargin: number;
      marginPercentage: number;
      total: number;
    };
    wed: {
      grossCost: number;
      fixedMargin: number;
      marginPercentage: number;
      total: number;
    };
    thu: {
      grossCost: number;
      fixedMargin: number;
      marginPercentage: number;
      total: number;
    };
    fri: {
      grossCost: number;
      fixedMargin: number;
      marginPercentage: number;
      total: number;
    };
    sat: {
      grossCost: number;
      fixedMargin: number;
      marginPercentage: number;
      total: number;
    };
    sun: {
      grossCost: number;
      fixedMargin: number;
      marginPercentage: number;
      total: number;
    };
  };
  modified: boolean;
  appliedToAllDays?: boolean; // Flag to track if Apply button has been used
};

type RoomPricingData = {
  room_config_id: string;
  room_config: RoomConfig;
  weekday_rules: any[];
  seasonal_prices: any[];
};

type ComprehensivePricingTableProps = {
  hotelId: string;
  roomConfigs: RoomConfig[];
  occupancyConfigs: OccupancyConfig[];
  mealPlans: MealPlan[];
  seasonalPeriods: SeasonalPeriod[];
  setSeasonalPeriods: React.Dispatch<React.SetStateAction<SeasonalPeriod[]>>;
  initialPrices?: Record<string, any>; // Legacy support
  roomPricingData?: RoomPricingData[]; // New comprehensive API data
  onSave?: (data: any) => void | Promise<void>;
  canEdit?: boolean;
  canCreate?: boolean;
  canDelete?: boolean;
  hideBackButton?: boolean;
};

const ComprehensivePricingTable: React.FC<ComprehensivePricingTableProps> = ({
  hotelId: _hotelId, // Prefix with underscore to indicate intentionally unused
  roomConfigs,
  occupancyConfigs,
  mealPlans,
  seasonalPeriods,
  setSeasonalPeriods,
  initialPrices = {},
  roomPricingData = [],
  onSave,
  canEdit = false,
  canCreate = false,
  canDelete = false,
  hideBackButton = false,
}) => {
  const [pricingRows, setPricingRows] = useState<PricingRow[]>([]);
  const [currencyCode, setCurrencyCode] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedRoomConfig, setSelectedRoomConfig] = useState<string>("");
  const [selectedOccupancyConfig, setSelectedOccupancyConfig] =
    useState<string>("");
  const [selectedMealPlan, setSelectedMealPlan] = useState<string>("");
  const [selectedSeasonFilter, setSelectedSeasonFilter] =
    useState<string>("all");
  const [showAllDays] = useState(true); // Always show all days for now
  const [showWeekdayCostMargin, setShowWeekdayCostMargin] = useState(false); // Toggle for weekday-specific cost/margin inputs

  // Function to auto-populate weekday cost/margin fields when toggle is activated
  const handleToggleWeekdayCostMargin = () => {
    const newShowState = !showWeekdayCostMargin;
    setShowWeekdayCostMargin(newShowState);

    // If toggling ON, auto-populate weekday fields with default values where weekday data is empty
    if (newShowState) {
      const updatedPricingRows = pricingRows.map((row) => {
        // Check if weekday values are empty (all zeros) and default values exist
        const hasDefaultValues =
          row.defaultValues.grossCost > 0 ||
          row.defaultValues.fixedMargin > 0 ||
          row.defaultValues.marginPercentage > 0;
        const weekdayValuesEmpty = Object.values(row.weekdayValues).every(
          (day: any) =>
            day.grossCost === 0 &&
            day.fixedMargin === 0 &&
            day.marginPercentage === 0
        );

        if (hasDefaultValues && weekdayValuesEmpty && !row.appliedToAllDays) {
          // Copy default values to all weekdays (only if Apply button hasn't been used)
          const weekdays: (keyof typeof row.weekdayValues)[] = [
            "mon",
            "tue",
            "wed",
            "thu",
            "fri",
            "sat",
            "sun",
          ];
          const updatedWeekdayValues = { ...row.weekdayValues };

          weekdays.forEach((day) => {
            updatedWeekdayValues[day] = {
              grossCost: row.defaultValues.grossCost,
              fixedMargin: row.defaultValues.fixedMargin,
              marginPercentage: row.defaultValues.marginPercentage,
              total: row.defaultValues.total,
            };
          });

          return {
            ...row,
            weekdayValues: updatedWeekdayValues,
            modified: true,
          };
        }

        return row;
      });

      setPricingRows(updatedPricingRows);
    }
  };

  const {
    currencies,
    defaultCurrency,
    isLoading: isLoadingCurrencies,
  } = useAdminCurrencies();

  // Get current currency object for proper formatting
  const currentCurrency =
    currencies.find((c: any) => c.currency_code === currencyCode) ||
    defaultCurrency;

  // Helper function to format price display
  const formatPrice = (amount: number | null | undefined): string => {
    if (!amount) return "";

    // The amount is already in display units (dollars) from the data loading
    // Return as string without forcing decimal places
    return amount.toString();
  };

  // Helper function to get currency symbol
  const getCurrencySymbol = (): string => {
    return currentCurrency?.symbol || currencyCode;
  };

  // Helper function to detect if a currency value needs conversion from cents to display units
  const needsCurrencyConversion = (
    value: string | number | null | undefined
  ): boolean => {
    if (!value) return false;

    // Convert to number for analysis
    const numValue = typeof value === "string" ? parseFloat(value) : value;
    if (typeof numValue !== "number" || isNaN(numValue)) return false;

    // Detection logic:
    // 1. String values likely represent cents (legacy format)
    // 2. Large numeric values (> 1000) likely represent cents
    // 3. Small numeric values (≤ 1000) likely represent display units

    if (typeof value === "string") {
      // String values are typically stored as cents in legacy systems
      return true;
    }

    // For numeric values, use threshold-based detection
    // Values > 1000 are likely in cents (e.g., 2400 cents = 24.00 CHF)
    // Values ≤ 1000 are likely in display units (e.g., 24.00 CHF)
    return numValue > 1000;
  };

  // Helper function to safely convert currency values from database to display format with intelligent detection
  const convertCurrencyValue = (
    value: string | number | null | undefined
  ): number => {
    if (!value) return 0;

    // Convert string to number if needed
    const numValue = typeof value === "string" ? parseFloat(value) : value;
    if (typeof numValue !== "number" || isNaN(numValue)) return 0;

    // Apply conversion detection logic
    const needsConversion = needsCurrencyConversion(value);
    const result = needsConversion ? numValue / 100 : numValue;

    // Debug logging for currency conversion detection
    if (numValue !== 0) {
      console.log(
        `💱 Currency conversion: ${value} (${typeof value}) → ${result} (needs conversion: ${needsConversion})`
      );
    }

    return result;
  };

  // Helper function to format currency values for API calls with intelligent detection
  const formatCurrencyForAPI = (value: number): number => {
    // For now, assume the backend expects values in the same format as they're stored
    // This can be enhanced later if we need to detect the expected API format
    // Based on the user's clarification, backend handles conversion, so send values directly
    return value;
  };

  // Test function to verify currency conversion detection logic
  const testCurrencyConversion = () => {
    console.log("🧪 Testing currency conversion detection:");

    // Test cases based on the provided data structure
    const testCases = [
      { value: 1200, expected: 12.0, description: "gross_cost from API" },
      { value: 2400, expected: 24.0, description: "total from API" },
      { value: "2400", expected: 24.0, description: "string value (legacy)" },
      { value: 24.0, expected: 24.0, description: "already in display units" },
      { value: 0, expected: 0, description: "zero value" },
      { value: 500, expected: 500, description: "small value (≤ 1000)" },
      { value: 1500, expected: 15.0, description: "large value (> 1000)" },
    ];

    testCases.forEach(({ value, expected, description }) => {
      const result = convertCurrencyValue(value);
      const passed = Math.abs(result - expected) < 0.01; // Allow for floating point precision
      console.log(
        `  ${
          passed ? "✅" : "❌"
        } ${description}: ${value} → ${result} (expected: ${expected})`
      );
    });
  };

  // Helper function to safely convert percentage values from database to display format
  const convertPercentageValue = (
    value: string | number | null | undefined
  ): number => {
    if (!value) return 0;

    // Convert string to number if needed
    const numValue = typeof value === "string" ? parseFloat(value) : value;
    if (typeof numValue !== "number" || isNaN(numValue)) return 0;

    // If the value is > 100, it's likely stored as basis points (e.g., 1000 = 10%)
    // If the value is <= 100, it's likely stored as percentage (e.g., 10 = 10%)
    const result = numValue > 100 ? numValue / 100 : numValue;
    return typeof result === "number" && !isNaN(result) ? result : 0;
  };

  // Helper function to parse user input (display format) to component state format
  const parsePrice = (displayValue: string): number => {
    // Parse the display value and return it as-is for component state
    // The component state stores values in display format (dollars)
    return parseFloat(displayValue) || 0;
  };

  // Helper function to get available meal plans for an occupancy type
  const getAvailableMealPlans = (occupancyTypeId: string) => {
    const occupancy = occupancyConfigs.find((oc) => oc.id === occupancyTypeId);
    if (!occupancy) return mealPlans;

    // Legacy logic for special accommodations (backward compatibility)
    const isExtraBed =
      (occupancy as any).type === "EXTRA_BED" ||
      occupancy.name?.toLowerCase().includes("extra bed");
    const isCot =
      (occupancy as any).type === "COT" ||
      occupancy.name?.toLowerCase().includes("cot");

    if (isExtraBed || isCot) {
      return [{ id: null, name: "N/A" }];
    }

    // Filter meal plans based on their applicable_occupancy_types metadata
    const availableMealPlans = mealPlans.filter((mealPlan) => {
      const applicableTypes = mealPlan.metadata?.applicable_occupancy_types;

      // If no applicable types specified, meal plan is available for all occupancy types
      if (!applicableTypes || applicableTypes.length === 0) {
        return true;
      }

      // Check if this occupancy type is in the applicable types
      return applicableTypes.includes(occupancyTypeId);
    });

    // If no meal plans are available, show N/A
    if (availableMealPlans.length === 0) {
      return [{ id: null, name: "N/A" }];
    }

    return availableMealPlans;
  };

  // State for seasonal period modal
  const [isSeasonalModalOpen, setIsSeasonalModalOpen] = useState(false);
  const [newSeasonName, setNewSeasonName] = useState("");

  // State for bulk price update modal
  const [isBulkUpdateModalOpen, setIsBulkUpdateModalOpen] = useState(false);

  // Initialize dates with noon time to avoid timezone issues
  const today = new Date();
  today.setHours(12, 0, 0, 0);

  const nextWeek = new Date(today);
  nextWeek.setDate(today.getDate() + 7);

  const [newSeasonStartDate, setNewSeasonStartDate] = useState<Date>(today);
  const [newSeasonEndDate, setNewSeasonEndDate] = useState<Date>(nextWeek);
  const [isAddingSeasonalPeriod, setIsAddingSeasonalPeriod] = useState(false);

  // Removed savePricing hook to avoid duplicate API calls - using direct fetch instead

  const weekdays = [
    { id: "mon", name: "Monday" },
    { id: "tue", name: "Tuesday" },
    { id: "wed", name: "Wednesday" },
    { id: "thu", name: "Thursday" },
    { id: "fri", name: "Friday" },
    { id: "sat", name: "Saturday" },
    { id: "sun", name: "Sunday" },
  ];

  // Set default currency when currencies are loaded
  useEffect(() => {
    if (defaultCurrency && !currencyCode) {
      setCurrencyCode(defaultCurrency.currency_code);
    }
  }, [defaultCurrency, currencyCode]);

  // Initialize data when components load AND currency is set
  useEffect(() => {
    if (!isLoadingCurrencies && currencyCode) {
      initializeData();
    }
  }, [
    initialPrices,
    roomPricingData,
    roomConfigs,
    occupancyConfigs,
    mealPlans,
    seasonalPeriods,
    isLoadingCurrencies,
    currencyCode, // Add currencyCode as dependency
  ]);

  const initializeData = (targetCurrency?: string) => {
    const activeCurrency = targetCurrency || currencyCode;

    // Run currency conversion tests on first initialization
    if (!targetCurrency) {
      testCurrencyConversion();
    }

    // Don't initialize if no currency is set
    if (!activeCurrency) {
      return;
    }

    setIsLoading(true);

    const newRows: PricingRow[] = [];

    // For each room config, create pricing rows for all combinations
    roomConfigs.forEach((room) => {
      // Get pricing data for this room - try new structure first, then fall back to legacy
      let currencyFilteredPrices: any = {
        weekday_rules: [],
        seasonal_prices: [],
      };

      // Method 1: Try new comprehensive API structure (roomPricingData)
      if (roomPricingData && roomPricingData.length > 0) {
        const roomData = roomPricingData.find(
          (data) => data.room_config_id === room.id
        );
        if (roomData) {
          // Filter weekday rules by currency
          const weekdayRules = roomData.weekday_rules.filter(
            (rule) =>
              rule.currency_code === activeCurrency || !rule.currency_code
          );

          // Filter seasonal prices by currency
          const seasonalPrices = roomData.seasonal_prices.filter(
            (price) =>
              price.currency_code === activeCurrency || !price.currency_code
          );

          currencyFilteredPrices = {
            weekday_rules: weekdayRules,
            seasonal_prices: seasonalPrices,
          };
        }
      }

      // Method 2: Fall back to legacy initialPrices structure
      if (
        currencyFilteredPrices.weekday_rules.length === 0 &&
        initialPrices[room.id]
      ) {
        const roomPrices = initialPrices[room.id] || {};

        if (roomPrices[activeCurrency]) {
          // Multi-currency structure - look for currency-specific data
          console.log(
            `Room ${room.id} - Found ${activeCurrency} data in legacy structure:`,
            roomPrices[activeCurrency]
          );
          currencyFilteredPrices = roomPrices[activeCurrency];
        } else if (roomPrices.currency_code === activeCurrency) {
          // Legacy single currency structure - direct match
          console.log(
            `Room ${room.id} - Using legacy single currency data for ${activeCurrency}`
          );
          currencyFilteredPrices = roomPrices;
        }
      }

      // Create base pricing rows (no seasonal period)
      occupancyConfigs.forEach((occupancy) => {
        // Get available meal plans for this occupancy type
        const mealPlansToProcess = getAvailableMealPlans(occupancy.id);

        mealPlansToProcess.forEach((mealPlan) => {
          // Check if we have a price for this combination
          const existingRule = currencyFilteredPrices.weekday_rules?.find(
            (rule: any) =>
              rule.occupancy_type_id === occupancy.id &&
              (mealPlan.id === null
                ? !rule.meal_plan_id || rule.meal_plan_id === null
                : rule.meal_plan_id === mealPlan.id)
          );

          // Create pricing row with weekday prices
          newRows.push({
            id: existingRule?.id || generateId(),
            roomConfigId: room.id,
            occupancyTypeId: occupancy.id,
            mealPlanId: mealPlan.id,
            seasonalPeriodId: undefined, // Base pricing
            prices: {
              mon: convertCurrencyValue(existingRule?.weekday_prices?.mon),
              tue: convertCurrencyValue(existingRule?.weekday_prices?.tue),
              wed: convertCurrencyValue(existingRule?.weekday_prices?.wed),
              thu: convertCurrencyValue(existingRule?.weekday_prices?.thu),
              fri: convertCurrencyValue(existingRule?.weekday_prices?.fri),
              sat: convertCurrencyValue(existingRule?.weekday_prices?.sat),
              sun: convertCurrencyValue(existingRule?.weekday_prices?.sun),
            },
            // Initialize default cost and margin values
            defaultValues: {
              grossCost: convertCurrencyValue(
                existingRule?.default_values?.gross_cost
              ),
              fixedMargin: convertCurrencyValue(
                existingRule?.default_values?.fixed_margin
              ),
              marginPercentage: convertPercentageValue(
                existingRule?.default_values?.margin_percentage
              ),
              total: convertCurrencyValue(existingRule?.default_values?.total),
            },
            // Initialize weekday-specific cost and margin values
            // Use new weekday_values structure if available, fallback to cost_margin_data or legacy fields
            weekdayValues: {
              mon: {
                grossCost: convertCurrencyValue(
                  existingRule?.weekday_values?.mon?.gross_cost ||
                    existingRule?.cost_margin_data?.mon?.gross_cost ||
                    existingRule?.monday_gross_cost
                ),
                fixedMargin: convertCurrencyValue(
                  existingRule?.weekday_values?.mon?.fixed_margin ||
                    existingRule?.cost_margin_data?.mon?.fixed_margin ||
                    existingRule?.monday_fixed_margin
                ),
                marginPercentage: (() => {
                  // Show exactly what we have for this specific weekday - no fallbacks to defaults
                  const weekdayValue =
                    existingRule?.weekday_values?.mon?.margin_percentage;
                  const costMarginValue =
                    existingRule?.cost_margin_data?.mon?.margin_percentage;

                  // Use the most specific weekday data available, or empty if none
                  const actualValue = weekdayValue ?? costMarginValue;

                  return actualValue !== undefined && actualValue !== null
                    ? convertPercentageValue(actualValue)
                    : 0; // Empty if no weekday-specific data
                })(),
                total: convertCurrencyValue(existingRule?.monday_price),
              },
              tue: {
                grossCost: convertCurrencyValue(
                  existingRule?.weekday_values?.tue?.gross_cost ||
                    existingRule?.cost_margin_data?.tue?.gross_cost ||
                    existingRule?.tuesday_gross_cost
                ),
                fixedMargin: convertCurrencyValue(
                  existingRule?.weekday_values?.tue?.fixed_margin ||
                    existingRule?.cost_margin_data?.tue?.fixed_margin ||
                    existingRule?.tuesday_fixed_margin
                ),
                marginPercentage: (() => {
                  // Show exactly what we have for this specific weekday - no fallbacks to defaults
                  const weekdayValue =
                    existingRule?.weekday_values?.tue?.margin_percentage;
                  const costMarginValue =
                    existingRule?.cost_margin_data?.tue?.margin_percentage;

                  // Use the most specific weekday data available, or empty if none
                  const actualValue = weekdayValue ?? costMarginValue;

                  return actualValue !== undefined && actualValue !== null
                    ? convertPercentageValue(actualValue)
                    : 0; // Empty if no weekday-specific data
                })(),
                total: convertCurrencyValue(existingRule?.tuesday_price),
              },
              wed: {
                grossCost: convertCurrencyValue(
                  existingRule?.weekday_values?.wed?.gross_cost ||
                    existingRule?.cost_margin_data?.wed?.gross_cost ||
                    existingRule?.wednesday_gross_cost
                ),
                fixedMargin: convertCurrencyValue(
                  existingRule?.weekday_values?.wed?.fixed_margin ||
                    existingRule?.cost_margin_data?.wed?.fixed_margin ||
                    existingRule?.wednesday_fixed_margin
                ),
                marginPercentage: (() => {
                  // Show exactly what we have for this specific weekday - no fallbacks to defaults
                  const weekdayValue =
                    existingRule?.weekday_values?.wed?.margin_percentage;
                  const costMarginValue =
                    existingRule?.cost_margin_data?.wed?.margin_percentage;

                  // Use the most specific weekday data available, or empty if none
                  const actualValue = weekdayValue ?? costMarginValue;

                  return actualValue !== undefined && actualValue !== null
                    ? convertPercentageValue(actualValue)
                    : 0; // Empty if no weekday-specific data
                })(),
                total: convertCurrencyValue(existingRule?.wednesday_price),
              },
              thu: {
                grossCost: convertCurrencyValue(
                  existingRule?.weekday_values?.thu?.gross_cost ||
                    existingRule?.cost_margin_data?.thu?.gross_cost ||
                    existingRule?.thursday_gross_cost
                ),
                fixedMargin: convertCurrencyValue(
                  existingRule?.weekday_values?.thu?.fixed_margin ||
                    existingRule?.cost_margin_data?.thu?.fixed_margin ||
                    existingRule?.thursday_fixed_margin
                ),
                marginPercentage: (() => {
                  // Show exactly what we have for this specific weekday - no fallbacks to defaults
                  const weekdayValue =
                    existingRule?.weekday_values?.thu?.margin_percentage;
                  const costMarginValue =
                    existingRule?.cost_margin_data?.thu?.margin_percentage;

                  // Use the most specific weekday data available, or empty if none
                  const actualValue = weekdayValue ?? costMarginValue;

                  return actualValue !== undefined && actualValue !== null
                    ? convertPercentageValue(actualValue)
                    : 0; // Empty if no weekday-specific data
                })(),
                total: convertCurrencyValue(existingRule?.thursday_price),
              },
              fri: {
                grossCost: convertCurrencyValue(
                  existingRule?.weekday_values?.fri?.gross_cost ||
                    existingRule?.cost_margin_data?.fri?.gross_cost ||
                    existingRule?.friday_gross_cost
                ),
                fixedMargin: convertCurrencyValue(
                  existingRule?.weekday_values?.fri?.fixed_margin ||
                    existingRule?.cost_margin_data?.fri?.fixed_margin ||
                    existingRule?.friday_fixed_margin
                ),
                marginPercentage: (() => {
                  // Show exactly what we have for this specific weekday - no fallbacks to defaults
                  const weekdayValue =
                    existingRule?.weekday_values?.fri?.margin_percentage;
                  const costMarginValue =
                    existingRule?.cost_margin_data?.fri?.margin_percentage;

                  // Use the most specific weekday data available, or empty if none
                  const actualValue = weekdayValue ?? costMarginValue;

                  return actualValue !== undefined && actualValue !== null
                    ? convertPercentageValue(actualValue)
                    : 0; // Empty if no weekday-specific data
                })(),
                total: convertCurrencyValue(existingRule?.friday_price),
              },
              sat: {
                grossCost: convertCurrencyValue(
                  existingRule?.weekday_values?.sat?.gross_cost ||
                    existingRule?.cost_margin_data?.sat?.gross_cost ||
                    existingRule?.saturday_gross_cost
                ),
                fixedMargin: convertCurrencyValue(
                  existingRule?.weekday_values?.sat?.fixed_margin ||
                    existingRule?.cost_margin_data?.sat?.fixed_margin ||
                    existingRule?.saturday_fixed_margin
                ),
                marginPercentage: (() => {
                  // Show exactly what we have for this specific weekday - no fallbacks to defaults
                  const weekdayValue =
                    existingRule?.weekday_values?.sat?.margin_percentage;
                  const costMarginValue =
                    existingRule?.cost_margin_data?.sat?.margin_percentage;

                  // Use the most specific weekday data available, or empty if none
                  const actualValue = weekdayValue ?? costMarginValue;

                  return actualValue !== undefined && actualValue !== null
                    ? convertPercentageValue(actualValue)
                    : 0; // Empty if no weekday-specific data
                })(),
                total: convertCurrencyValue(existingRule?.saturday_price),
              },
              sun: {
                grossCost: convertCurrencyValue(
                  existingRule?.weekday_values?.sun?.gross_cost ||
                    existingRule?.cost_margin_data?.sun?.gross_cost ||
                    existingRule?.sunday_gross_cost
                ),
                fixedMargin: convertCurrencyValue(
                  existingRule?.weekday_values?.sun?.fixed_margin ||
                    existingRule?.cost_margin_data?.sun?.fixed_margin ||
                    existingRule?.sunday_fixed_margin
                ),
                marginPercentage: (() => {
                  // Show exactly what we have for this specific weekday - no fallbacks to defaults
                  const weekdayValue =
                    existingRule?.weekday_values?.sun?.margin_percentage;
                  const costMarginValue =
                    existingRule?.cost_margin_data?.sun?.margin_percentage;

                  // Use the most specific weekday data available, or empty if none
                  const actualValue = weekdayValue ?? costMarginValue;

                  return actualValue !== undefined && actualValue !== null
                    ? convertPercentageValue(actualValue)
                    : 0; // Empty if no weekday-specific data
                })(),
                total: convertCurrencyValue(existingRule?.sunday_price),
              },
            },
            modified: false,
          });

          // Create seasonal pricing rows
          seasonalPeriods.forEach((season) => {
            // Check if we have seasonal prices for this combination
            const seasonalPrices = currencyFilteredPrices.seasonal_prices || [];
            const existingSeasonalPrice = seasonalPrices.find(
              (sp: any) => sp.id === season.id || sp.name === season.name
            );

            const existingSeasonalRule =
              existingSeasonalPrice?.weekday_rules?.find(
                (rule: any) =>
                  rule.occupancy_type_id === occupancy.id &&
                  (mealPlan.id === null
                    ? !rule.meal_plan_id || rule.meal_plan_id === null
                    : rule.meal_plan_id === mealPlan.id)
              );

            // Create pricing row with weekday prices for this season
            newRows.push({
              id: existingSeasonalRule?.id || generateId(),
              roomConfigId: room.id,
              occupancyTypeId: occupancy.id,
              mealPlanId: mealPlan.id,
              seasonalPeriodId: season.id,
              prices: {
                mon: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.mon || 0) / 100
                  : 0,
                tue: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.tue || 0) / 100
                  : 0,
                wed: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.wed || 0) / 100
                  : 0,
                thu: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.thu || 0) / 100
                  : 0,
                fri: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.fri || 0) / 100
                  : 0,
                sat: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.sat || 0) / 100
                  : 0,
                sun: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.sun || 0) / 100
                  : 0,
              },
              // Initialize default cost and margin values for seasonal pricing
              defaultValues: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: 0,
              },
              // Initialize weekday-specific cost and margin values for seasonal pricing
              weekdayValues: {
                mon: {
                  grossCost: 0,
                  fixedMargin: 0,
                  marginPercentage: 0,
                  total: 0,
                },
                tue: {
                  grossCost: 0,
                  fixedMargin: 0,
                  marginPercentage: 0,
                  total: 0,
                },
                wed: {
                  grossCost: 0,
                  fixedMargin: 0,
                  marginPercentage: 0,
                  total: 0,
                },
                thu: {
                  grossCost: 0,
                  fixedMargin: 0,
                  marginPercentage: 0,
                  total: 0,
                },
                fri: {
                  grossCost: 0,
                  fixedMargin: 0,
                  marginPercentage: 0,
                  total: 0,
                },
                sat: {
                  grossCost: 0,
                  fixedMargin: 0,
                  marginPercentage: 0,
                  total: 0,
                },
                sun: {
                  grossCost: 0,
                  fixedMargin: 0,
                  marginPercentage: 0,
                  total: 0,
                },
              },
              modified: false,
            });
          });
        });
      });
    });

    setPricingRows(newRows);
    setIsLoading(false);
  };

  const handlePriceChange = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null,
    seasonalPeriodId: string | undefined,
    day: string,
    value: number
  ) => {
    setPricingRows((prev) =>
      prev.map((row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId === seasonalPeriodId
          ? {
              ...row,
              prices: { ...row.prices, [day]: value },
              modified: true,
            }
          : row
      )
    );
  };

  const handleCurrencyChange = (newCurrencyCode: string) => {
    console.log(
      `Currency change requested: ${currencyCode} → ${newCurrencyCode}`
    );
    setCurrencyCode(newCurrencyCode);
    // Re-initialize data with the new currency to filter pricing data
    // Pass the new currency directly to avoid state timing issues
    initializeData(newCurrencyCode);
  };

  const handleCopyBaseToSeasonal = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null
  ) => {
    // Find the base pricing row
    const basePricingRow = pricingRows.find(
      (row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        !row.seasonalPeriodId
    );

    if (!basePricingRow) return;

    // Update all seasonal pricing rows for this combination
    setPricingRows((prev) =>
      prev.map((row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId // Only seasonal rows
          ? {
              ...row,
              prices: { ...basePricingRow.prices },
              modified: true,
            }
          : row
      )
    );

    toast.success("Success", {
      description: "Base prices copied to all seasonal periods",
    });
  };

  console.log({ pricingRows });

  const handleCopyToAllDays = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null,
    seasonalPeriodId: string | undefined,
    sourceDay: string
  ) => {
    // Find the pricing row
    const pricingRow = pricingRows.find(
      (row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId === seasonalPeriodId
    );

    if (!pricingRow) return;

    // Get the price from the source day
    const sourcePrice =
      pricingRow.prices[sourceDay as keyof typeof pricingRow.prices];

    // Create new prices with the same value for all days
    const newPrices = {
      mon: sourcePrice,
      tue: sourcePrice,
      wed: sourcePrice,
      thu: sourcePrice,
      fri: sourcePrice,
      sat: sourcePrice,
      sun: sourcePrice,
    };

    // Update the pricing row
    setPricingRows((prev) =>
      prev.map((row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId === seasonalPeriodId
          ? {
              ...row,
              prices: newPrices,
              modified: true,
            }
          : row
      )
    );

    toast.success("Success", {
      description: `${sourceDay.toUpperCase()} price copied to all days`,
    });
  };

  const handleBulkPriceUpdate = (updatedRows: PricingRow[]) => {
    setPricingRows(updatedRows);
    toast.success("Success", {
      description: "Bulk price update applied successfully",
    });
  };

  // Handle changes to default cost and margin values with REAL-TIME CALCULATION
  const handleDefaultValueChange = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null,
    seasonalPeriodId: string | undefined,
    field: "grossCost" | "fixedMargin" | "marginPercentage",
    value: number
  ) => {
    console.log(`📝 Default value change: ${field} = ${value}`);

    setPricingRows((prev) =>
      prev.map((row) => {
        if (
          row.roomConfigId === roomConfigId &&
          row.occupancyTypeId === occupancyTypeId &&
          row.mealPlanId === mealPlanId &&
          row.seasonalPeriodId === seasonalPeriodId
        ) {
          // Update the specific field first
          const updatedDefaultValues = {
            ...row.defaultValues,
            [field]: value,
          };

          // MUTUAL EXCLUSION LOGIC: Clear the opposite margin field when one is entered
          if (field === "fixedMargin" && value > 0) {
            // When Fixed Margin is entered, clear Margin Percentage for defaults
            updatedDefaultValues.marginPercentage = 0;
            console.log(
              `🔄 Mutual exclusion: Cleared default margin percentage because fixed margin was set to ${value}`
            );
          } else if (field === "marginPercentage" && value > 0) {
            // When Margin Percentage is entered, clear Fixed Margin for defaults
            updatedDefaultValues.fixedMargin = 0;
            console.log(
              `🔄 Mutual exclusion: Cleared default fixed margin because margin percentage was set to ${value}`
            );
          }

          // Calculate the new total automatically using the cost-margin calculator
          const calculatedTotal = calculateTotalFromCostMargin({
            gross_cost: updatedDefaultValues.grossCost, // Backend handles conversion
            fixed_margin: updatedDefaultValues.fixedMargin, // Backend handles conversion
            margin_percentage: updatedDefaultValues.marginPercentage, // Percentage stays as-is
          });

          // Update the total field with calculated value
          if (calculatedTotal !== null && calculatedTotal !== undefined) {
            updatedDefaultValues.total = calculatedTotal; // Use calculated value directly
            console.log(
              `🧮 Auto-calculated default total: cost=${updatedDefaultValues.grossCost}, margin=${updatedDefaultValues.fixedMargin}, %=${updatedDefaultValues.marginPercentage} → total=${updatedDefaultValues.total}`
            );
          } else {
            // If calculation fails, keep existing total
            console.log(
              `⚠️ Calculation failed, keeping existing total: ${updatedDefaultValues.total}`
            );
          }

          return {
            ...row,
            defaultValues: updatedDefaultValues,
            modified: true,
          };
        }
        return row;
      })
    );
  };

  // Handle changes to weekday-specific cost and margin values with automatic calculation
  const handleWeekdayValueChange = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null,
    seasonalPeriodId: string | undefined,
    day: "mon" | "tue" | "wed" | "thu" | "fri" | "sat" | "sun",
    field: "grossCost" | "fixedMargin" | "marginPercentage",
    value: number
  ) => {
    console.log(`📝 Weekday value change: ${day} ${field} = ${value}`);

    setPricingRows((prev) =>
      prev.map((row) => {
        if (
          row.roomConfigId === roomConfigId &&
          row.occupancyTypeId === occupancyTypeId &&
          row.mealPlanId === mealPlanId &&
          row.seasonalPeriodId === seasonalPeriodId
        ) {
          // Update the field value first
          const updatedWeekdayValues = {
            ...row.weekdayValues,
            [day]: {
              ...row.weekdayValues[day],
              [field]: value,
            },
          };

          // MUTUAL EXCLUSION LOGIC: Clear the opposite margin field when one is entered
          if (field === "fixedMargin" && value > 0) {
            // When Fixed Margin is entered, clear Margin Percentage for this weekday
            updatedWeekdayValues[day] = {
              ...updatedWeekdayValues[day],
              marginPercentage: 0,
            };
            console.log(
              `🔄 Mutual exclusion: Cleared ${day} margin percentage because fixed margin was set to ${value}`
            );
          } else if (field === "marginPercentage" && value > 0) {
            // When Margin Percentage is entered, clear Fixed Margin for this weekday
            updatedWeekdayValues[day] = {
              ...updatedWeekdayValues[day],
              fixedMargin: 0,
            };
            console.log(
              `🔄 Mutual exclusion: Cleared ${day} fixed margin because margin percentage was set to ${value}`
            );
          }

          // Automatically calculate total when cost/margin values change
          const dayValues = updatedWeekdayValues[day];

          // Use updated values for calculation, fall back to default if not set
          const grossCost =
            dayValues.grossCost !== undefined && dayValues.grossCost !== null
              ? dayValues.grossCost
              : row.defaultValues.grossCost;
          const fixedMargin =
            dayValues.fixedMargin !== undefined &&
            dayValues.fixedMargin !== null
              ? dayValues.fixedMargin
              : row.defaultValues.fixedMargin;
          // Use weekday-specific margin percentage if set, otherwise fall back to default
          const marginPercentage =
            dayValues.marginPercentage !== undefined &&
            dayValues.marginPercentage !== null
              ? dayValues.marginPercentage
              : row.defaultValues.marginPercentage;

          // Always calculate total, even when cost is 0
          const calculatedTotalValue = calculateTotalFromCostMargin({
            gross_cost: grossCost, // Backend handles conversion (0 is valid)
            fixed_margin: fixedMargin, // Backend handles conversion
            margin_percentage: marginPercentage, // Percentage stays as-is
          });

          let calculatedTotal = dayValues.total;
          if (
            calculatedTotalValue !== null &&
            calculatedTotalValue !== undefined
          ) {
            calculatedTotal = calculatedTotalValue; // Use calculated value directly
            console.log(
              `🔄 Auto-calculated ${day} total: ${calculatedTotal} CHF (cost=${grossCost}, margin=${fixedMargin}, %=${marginPercentage})`
            );
          }

          // Update the total in weekday values
          updatedWeekdayValues[day] = {
            ...updatedWeekdayValues[day],
            total: calculatedTotal,
          };

          return {
            ...row,
            weekdayValues: updatedWeekdayValues,
            // Also update the prices field for backward compatibility
            prices: {
              ...row.prices,
              [day]: calculatedTotal,
            },
            modified: true,
          };
        }
        return row;
      })
    );
  };

  // Handle applying weekday-specific values to all rows (LOCAL CALCULATION ONLY)
  const handleApplyWeekdayToAllRows = (
    day: "mon" | "tue" | "wed" | "thu" | "fri" | "sat" | "sun",
    sourceRow: PricingRow
  ) => {
    const sourceValues = sourceRow.weekdayValues[day];

    setPricingRows((prev) =>
      prev.map((row) => {
        // Apply to all rows in the same seasonal period (or base pricing)
        if (row.seasonalPeriodId === sourceRow.seasonalPeriodId) {
          const updatedWeekdayValues = {
            ...row.weekdayValues,
            [day]: {
              grossCost: sourceValues.grossCost,
              fixedMargin: sourceValues.fixedMargin,
              marginPercentage: sourceValues.marginPercentage,
              total: sourceValues.total,
            },
          };

          // Update the corresponding price in the prices object
          const updatedPrices = {
            ...row.prices,
            [day]: sourceValues.total,
          };

          return {
            ...row,
            weekdayValues: updatedWeekdayValues,
            prices: updatedPrices,
            modified: true,
          };
        }
        return row;
      })
    );
  };

  // Handle applying cost/margin calculations to all weekdays (LOCAL CALCULATION ONLY)
  const handleApplyToAllDays = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null,
    seasonalPeriodId: string | undefined
  ) => {
    // Find the pricing row
    const pricingRow = pricingRows.find(
      (row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId === seasonalPeriodId
    );

    if (!pricingRow) {
      toast.error("Error", {
        description: "Pricing row not found",
      });
      return;
    }

    // Check if we have valid default total (should be auto-calculated now)
    if (
      !pricingRow.defaultValues.total ||
      pricingRow.defaultValues.total <= 0
    ) {
      toast.error("Error", {
        description:
          "Please set valid default cost/margin values first. The total should be automatically calculated.",
      });
      return;
    }

    console.log(
      `🔄 Applying calculated default total (${pricingRow.defaultValues.total}) to all weekday price fields (LOCAL ONLY)`
    );

    // Use the calculated default total for all weekdays
    const defaultTotal = pricingRow.defaultValues.total;
    const weekdays: (keyof typeof pricingRow.weekdayValues)[] = [
      "mon",
      "tue",
      "wed",
      "thu",
      "fri",
      "sat",
      "sun",
    ];
    const calculatedTotals: { [key: string]: number } = {};
    const calculatedPrices: { [key: string]: number } = {};

    weekdays.forEach((day) => {
      // Apply the default total to all weekdays
      calculatedTotals[day] = defaultTotal;
      calculatedPrices[day] = defaultTotal;

      console.log(`  ${day}: Applied default total = ${defaultTotal} CHF`);
    });

    // Update the local state to populate all weekday fields with the calculated totals and clear cost/margin values
    setPricingRows((prev) =>
      prev.map((row) => {
        if (
          row.roomConfigId === roomConfigId &&
          row.occupancyTypeId === occupancyTypeId &&
          row.mealPlanId === mealPlanId &&
          row.seasonalPeriodId === seasonalPeriodId
        ) {
          return {
            ...row,
            // Update weekday totals and clear cost/margin values to avoid confusion
            weekdayValues: {
              mon: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: calculatedTotals.mon,
              },
              tue: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: calculatedTotals.tue,
              },
              wed: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: calculatedTotals.wed,
              },
              thu: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: calculatedTotals.thu,
              },
              fri: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: calculatedTotals.fri,
              },
              sat: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: calculatedTotals.sat,
              },
              sun: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: calculatedTotals.sun,
              },
            },
            // Also update prices for backward compatibility
            prices: {
              mon: calculatedPrices.mon,
              tue: calculatedPrices.tue,
              wed: calculatedPrices.wed,
              thu: calculatedPrices.thu,
              fri: calculatedPrices.fri,
              sat: calculatedPrices.sat,
              sun: calculatedPrices.sun,
            },
            modified: true,
            appliedToAllDays: true, // Mark that Apply button has been used
          };
        }
        return row;
      })
    );

    // Show success message
    toast.success("Applied default total to all weekday prices", {
      description: `Set all weekday prices to ${defaultTotal} CHF based on calculated default total`,
    });
  };

  const handleAddSeasonalPeriod = () => {
    // Validate inputs
    if (!newSeasonName.trim()) {
      toast.error("Error", {
        description: "Please enter a season name",
      });
      return;
    }

    // Make sure we have valid dates
    let startDate = newSeasonStartDate;
    let endDate = newSeasonEndDate;

    if (!startDate) {
      startDate = new Date();
    }

    if (!endDate) {
      endDate = new Date();
      // Set to 7 days after start date by default
      endDate.setDate(startDate.getDate() + 7);
    }

    if (startDate > endDate) {
      toast.error("Error", {
        description: "End date must be after start date",
      });
      return;
    }

    setIsAddingSeasonalPeriod(true);

    try {
      // Format dates safely
      const formattedStartDate = format(startDate, "yyyy-MM-dd");
      const formattedEndDate = format(endDate, "yyyy-MM-dd");

      // Check if season already exists
      const existingSeasonalPeriod = seasonalPeriods.find(
        (period) =>
          period.name.toLowerCase() === newSeasonName.trim().toLowerCase()
      );

      if (existingSeasonalPeriod) {
        toast.error("Error", {
          description: "A season with this name already exists",
        });
        return;
      }

      // Create new seasonal period
      const newSeasonalPeriod: SeasonalPeriod = {
        id: generateId(),
        name: newSeasonName.trim(),
        start_date: formattedStartDate,
        end_date: formattedEndDate,
      };

      // Add to seasonal periods list
      setSeasonalPeriods((prev) => [...prev, newSeasonalPeriod]);

      // Create new pricing rows for this seasonal period
      const newRows: PricingRow[] = [];

      roomConfigs.forEach((roomConfig) => {
        occupancyConfigs.forEach((occupancyConfig) => {
          // Add row without meal plan
          newRows.push({
            id: generateId(),
            roomConfigId: roomConfig.id,
            occupancyTypeId: occupancyConfig.id,
            mealPlanId: null,
            seasonalPeriodId: newSeasonalPeriod.id,
            prices: {
              mon: 0,
              tue: 0,
              wed: 0,
              thu: 0,
              fri: 0,
              sat: 0,
              sun: 0,
            },
            defaultValues: {
              grossCost: 0,
              fixedMargin: 0,
              marginPercentage: 0,
              total: 0,
            },
            weekdayValues: {
              mon: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: 0,
              },
              tue: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: 0,
              },
              wed: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: 0,
              },
              thu: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: 0,
              },
              fri: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: 0,
              },
              sat: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: 0,
              },
              sun: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: 0,
              },
            },
            modified: false,
          });

          // Add rows for each meal plan
          mealPlans.forEach((mealPlan) => {
            newRows.push({
              id: generateId(),
              roomConfigId: roomConfig.id,
              occupancyTypeId: occupancyConfig.id,
              mealPlanId: mealPlan.id,
              seasonalPeriodId: newSeasonalPeriod.id,
              prices: {
                mon: 0,
                tue: 0,
                wed: 0,
                thu: 0,
                fri: 0,
                sat: 0,
                sun: 0,
              },
              defaultValues: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: 0,
              },
              weekdayValues: {
                mon: {
                  grossCost: 0,
                  fixedMargin: 0,
                  marginPercentage: 0,
                  total: 0,
                },
                tue: {
                  grossCost: 0,
                  fixedMargin: 0,
                  marginPercentage: 0,
                  total: 0,
                },
                wed: {
                  grossCost: 0,
                  fixedMargin: 0,
                  marginPercentage: 0,
                  total: 0,
                },
                thu: {
                  grossCost: 0,
                  fixedMargin: 0,
                  marginPercentage: 0,
                  total: 0,
                },
                fri: {
                  grossCost: 0,
                  fixedMargin: 0,
                  marginPercentage: 0,
                  total: 0,
                },
                sat: {
                  grossCost: 0,
                  fixedMargin: 0,
                  marginPercentage: 0,
                  total: 0,
                },
                sun: {
                  grossCost: 0,
                  fixedMargin: 0,
                  marginPercentage: 0,
                  total: 0,
                },
              },
              modified: false,
            });
          });
        });
      });

      // Update pricing rows state
      setPricingRows((prev) => [...prev, ...newRows]);

      // Close the modal and reset form
      setIsSeasonalModalOpen(false);
      setNewSeasonName("");
      setNewSeasonStartDate(new Date());
      setNewSeasonEndDate(new Date());

      toast.success("Success", {
        description: "Seasonal period added successfully",
      });
    } catch (error) {
      console.error("Error adding seasonal period:", error);
      toast.error("Error", {
        description:
          "Failed to add seasonal period: " +
          (error instanceof Error ? error.message : String(error)),
      });
    } finally {
      setIsAddingSeasonalPeriod(false);
    }
  };

  const handleSaveAll = async () => {
    setIsSaving(true);

    try {
      console.log("=== SAVE ALL DEBUG START ===");

      // Group pricing rows by room config and seasonal period
      const roomConfigPrices: Record<string, any> = {};

      // Only initialize room config prices for rooms that have modified data
      // Don't create empty pricing records for all rooms

      // Process base pricing rows
      const basePricingRows = pricingRows.filter(
        (row) => !row.seasonalPeriodId
      );
      basePricingRows.forEach((row) => {
        // Only include rows that have been modified
        if (row.modified) {
          console.log(
            `Saving modified base pricing for room ${row.roomConfigId}, currency ${currencyCode}`
          );

          // Initialize room config pricing if not exists
          if (!roomConfigPrices[row.roomConfigId]) {
            roomConfigPrices[row.roomConfigId] = {
              currency_code: currencyCode,
              weekday_rules: [],
              seasonal_prices: [],
            };
          }

          // Create default values (backend handles currency conversion)
          const defaultValues = {
            gross_cost: row.defaultValues.grossCost, // Backend handles conversion
            fixed_margin: row.defaultValues.fixedMargin, // Backend handles conversion
            margin_percentage: row.defaultValues.marginPercentage, // Percentage stays as-is
            total: row.defaultValues.total, // Backend handles conversion
          };

          // Create weekday prices (backend handles currency conversion)
          const weekdayPrices = {
            mon: row.prices.mon, // Backend handles conversion
            tue: row.prices.tue, // Backend handles conversion
            wed: row.prices.wed, // Backend handles conversion
            thu: row.prices.thu, // Backend handles conversion
            fri: row.prices.fri, // Backend handles conversion
            sat: row.prices.sat, // Backend handles conversion
            sun: row.prices.sun, // Backend handles conversion
          };

          // Create weekday-specific cost and margin values (backend handles currency conversion)
          const weekdayValues = {
            mon: {
              gross_cost: row.weekdayValues.mon.grossCost, // Backend handles conversion
              fixed_margin: row.weekdayValues.mon.fixedMargin, // Backend handles conversion
              margin_percentage: row.weekdayValues.mon.marginPercentage, // Percentage stays as-is
              total: row.weekdayValues.mon.total, // Backend handles conversion
            },
            tue: {
              gross_cost: row.weekdayValues.tue.grossCost, // Backend handles conversion
              fixed_margin: row.weekdayValues.tue.fixedMargin, // Backend handles conversion
              margin_percentage: row.weekdayValues.tue.marginPercentage, // Percentage stays as-is
              total: row.weekdayValues.tue.total, // Backend handles conversion
            },
            wed: {
              gross_cost: row.weekdayValues.wed.grossCost, // Backend handles conversion
              fixed_margin: row.weekdayValues.wed.fixedMargin, // Backend handles conversion
              margin_percentage: row.weekdayValues.wed.marginPercentage, // Percentage stays as-is
              total: row.weekdayValues.wed.total, // Backend handles conversion
            },
            thu: {
              gross_cost: row.weekdayValues.thu.grossCost, // Backend handles conversion
              fixed_margin: row.weekdayValues.thu.fixedMargin, // Backend handles conversion
              margin_percentage: row.weekdayValues.thu.marginPercentage, // Percentage stays as-is
              total: row.weekdayValues.thu.total, // Backend handles conversion
            },
            fri: {
              gross_cost: row.weekdayValues.fri.grossCost, // Backend handles conversion
              fixed_margin: row.weekdayValues.fri.fixedMargin, // Backend handles conversion
              margin_percentage: row.weekdayValues.fri.marginPercentage, // Percentage stays as-is
              total: row.weekdayValues.fri.total, // Backend handles conversion
            },
            sat: {
              gross_cost: row.weekdayValues.sat.grossCost, // Backend handles conversion
              fixed_margin: row.weekdayValues.sat.fixedMargin, // Backend handles conversion
              margin_percentage: row.weekdayValues.sat.marginPercentage, // Percentage stays as-is
              total: row.weekdayValues.sat.total, // Backend handles conversion
            },
            sun: {
              gross_cost: row.weekdayValues.sun.grossCost, // Backend handles conversion
              fixed_margin: row.weekdayValues.sun.fixedMargin, // Backend handles conversion
              margin_percentage: row.weekdayValues.sun.marginPercentage, // Percentage stays as-is
              total: row.weekdayValues.sun.total, // Backend handles conversion
            },
          };

          console.log(
            `[FRONTEND] Sending restructured data for room ${row.roomConfigId}:`,
            {
              defaultValues: row.defaultValues,
              weekdayPrices: row.prices,
              convertedDefaultValues: defaultValues,
              convertedWeekdayPrices: weekdayPrices,
            }
          );

          roomConfigPrices[row.roomConfigId].weekday_rules.push({
            occupancy_type_id: row.occupancyTypeId,
            meal_plan_id: row.mealPlanId, // This will be null for extra beds
            default_values: defaultValues,
            weekday_prices: weekdayPrices,
            weekday_values: weekdayValues, // Include weekday-specific cost and margin data
          });
        }
      });

      // Process seasonal pricing rows - UPDATE existing seasonal pricing rules
      const seasonalPricingRows = pricingRows.filter(
        (row) => row.seasonalPeriodId && row.modified
      );

      console.log(
        `=== SEASONAL PRICING: ${seasonalPricingRows.length} seasonal pricing rules to update ===`
      );

      // Update each seasonal pricing rule individually
      const seasonalPricingPromises = seasonalPricingRows.map(async (row) => {
        console.log(
          `🌟 SEASONAL API CALL: Updating seasonal pricing rule for room ${row.roomConfigId}, occupancy ${row.occupancyTypeId}, meal plan ${row.mealPlanId}`
        );

        // Prepare weekday prices (backend handles currency conversion)
        const weekdayPrices = {
          mon: row.prices.mon, // Backend handles conversion
          tue: row.prices.tue, // Backend handles conversion
          wed: row.prices.wed, // Backend handles conversion
          thu: row.prices.thu, // Backend handles conversion
          fri: row.prices.fri, // Backend handles conversion
          sat: row.prices.sat, // Backend handles conversion
          sun: row.prices.sun, // Backend handles conversion
        };

        console.log(
          `Updating seasonal rule ${row.id} with weekday prices:`,
          weekdayPrices
        );

        const response = await fetch(
          `/admin/hotel-management/seasonal-pricing/${row.id}/update-weekday-prices`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              weekday_prices: weekdayPrices,
            }),
          }
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error(
            `Failed to update seasonal pricing rule ${row.id}: ${errorText}`
          );
          throw new Error(
            `Failed to update seasonal pricing rule: ${errorText}`
          );
        }

        const result = await response.json();
        console.log(`✅ Successfully updated seasonal pricing rule ${row.id}`);
        return result;
      });

      // Wait for all seasonal pricing rules to be updated
      const seasonalResults = await Promise.all(seasonalPricingPromises);

      // Save base pricing for each room config
      // FIXED: Only save room configs that actually have base pricing changes
      // Now that seasonal pricing is handled separately, roomConfigPrices should
      // only contain room configs with actual base pricing modifications
      console.log(
        `=== BASE PRICING: ${
          Object.keys(roomConfigPrices).length
        } room configs to process ===`
      );
      const savePromises = Object.entries(roomConfigPrices).map(
        async ([roomConfigId, data]) => {
          // Only save if there are weekday rules (base pricing)
          if (data.weekday_rules.length > 0) {
            console.log(
              `💰 BASE API CALL: Saving base pricing for room config ${roomConfigId} with ${data.weekday_rules.length} rules`
            );

            // FIXED: Use direct fetch instead of savePricing hook to avoid duplicate seasonal processing
            // The savePricing hook automatically processes seasonal_prices which we handle separately
            const response = await fetch(
              `/admin/hotel-management/room-configs/${roomConfigId}/weekday-pricing/bulk`,
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  currency_code: data.currency_code,
                  weekday_rules: data.weekday_rules,
                }),
              }
            );

            if (!response.ok) {
              const errorText = await response.text();
              console.error(
                `Failed to save base pricing for room ${roomConfigId}: ${errorText}`
              );
              throw new Error(`Failed to save base pricing: ${errorText}`);
            }

            return await response.json();
          } else {
            console.log(
              `⏭️  SKIPPING: Room config ${roomConfigId} has no base pricing changes`
            );
          }
          return null;
        }
      );

      const baseResults = await Promise.all(savePromises);
      const results = [...baseResults, ...seasonalResults];

      console.log(
        `=== SAVE ALL DEBUG END: ${seasonalResults.length} seasonal + ${
          baseResults.filter((r) => r !== null).length
        } base = ${
          results.filter((r) => r !== null).length
        } total API calls ===`
      );

      // Call onSave callback if provided and wait for it to complete
      if (onSave) {
        await onSave({
          results,
          seasonal_periods: seasonalPeriods,
        });
      }

      // Reset modified flags AFTER the data has been refreshed
      setPricingRows((prev) =>
        prev.map((row) => ({ ...row, modified: false }))
      );

      // Don't show success toast here - let the parent component handle it
      // The parent component will show the success message after refetching data
    } catch (error) {
      console.error("Error saving pricing:", error);
      toast.error("Error", {
        description: "Failed to save pricing",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Filter pricing rows based on selected filters
  const filteredPricingRows = pricingRows.filter((row) => {
    // Filter by room config if selected
    if (selectedRoomConfig && row.roomConfigId !== selectedRoomConfig) {
      return false;
    }

    // Filter by occupancy config if selected
    if (
      selectedOccupancyConfig &&
      row.occupancyTypeId !== selectedOccupancyConfig
    ) {
      return false;
    }

    // Filter by meal plan if selected
    if (selectedMealPlan && row.mealPlanId !== selectedMealPlan) {
      return false;
    }

    // Filter by season based on selected season filter
    if (selectedSeasonFilter === "base" && row.seasonalPeriodId) {
      return false; // Hide seasonal rows when showing base only
    }
    if (selectedSeasonFilter !== "all" && selectedSeasonFilter !== "base") {
      // Show only specific season
      if (row.seasonalPeriodId !== selectedSeasonFilter) {
        return false;
      }
    }

    return true;
  });

  if (isLoading) {
    return <HotelPricingTableSkeleton />;
  }

  // Reusable select component using @camped-ai/ui Select
  const FilterSelect = ({
    id,
    label,
    value,
    onChange,
    options,
    placeholder = "All",
  }: {
    id: string;
    label: string;
    value: string;
    onChange: (value: string) => void;
    options: Array<{ id: string; name?: string; title?: string }>;
    placeholder?: string;
  }) => (
    <div>
      <Label htmlFor={id} className="mb-1 block text-sm font-medium">
        {label}
      </Label>
      <Select
        value={value || "all"}
        onValueChange={(val) => onChange(val === "all" ? "" : val)}
      >
        <Select.Trigger id={id} className="w-full">
          <Select.Value placeholder={placeholder} />
        </Select.Trigger>
        <Select.Content>
          <Select.Item value="all">{placeholder}</Select.Item>
          {options.map((option) => (
            <Select.Item key={option.id} value={option.id}>
              {option.title || option.name}
            </Select.Item>
          ))}
        </Select.Content>
      </Select>
    </div>
  );

  return (
    <Container>
      <Toaster />

      {/* Compact Header with Controls */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
        {!hideBackButton && (
          <div>
            <Heading level="h2" className="mb-1">
              Hotel Pricing Management
            </Heading>
            <Text className=" text-sm">
              Manage base rates, seasonal pricing, and special offers for your
              hotel rooms
            </Text>
          </div>
        )}

        <div className="flex flex-wrap items-center gap-2">
          {currencies.length > 1 && (
            <CurrencySelector
              value={currencyCode}
              onChange={handleCurrencyChange}
              label="Currency"
              id="currency"
            />
          )}

          {canEdit && (
            <Button
              variant="secondary"
              onClick={() => setIsBulkUpdateModalOpen(true)}
              className="flex items-center gap-2 h-9 text-sm"
              title="Update multiple prices at once"
            >
              <Calculator className="w-4 h-4" />
              Bulk Update
            </Button>
          )}

          <Button
            variant={showWeekdayCostMargin ? "primary" : "secondary"}
            onClick={handleToggleWeekdayCostMargin}
            className="flex items-center gap-2 h-9 text-sm"
            title="Toggle weekday-specific cost and margin inputs"
          >
            <TrendingUp className="w-4 h-4" />
            {showWeekdayCostMargin ? "Hide" : "Show"} Weekday Cost/Margin
          </Button>

          {canCreate && (
            <Button
              variant="primary"
              onClick={() => setIsSeasonalModalOpen(true)}
              className="flex items-center gap-2 h-9 text-sm"
            >
              <Calendar className="w-4 h-4" />
              Add Season
            </Button>
          )}

          {canEdit && (
            <Button
              variant="primary"
              onClick={handleSaveAll}
              className="flex items-center gap-2 h-9 text-sm"
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  Save All
                </>
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Seasonal Period Modal */}
      <Drawer
        open={isSeasonalModalOpen}
        onOpenChange={(open) => {
          setIsSeasonalModalOpen(open);
          if (!open) {
            // Reset form when closing
            setNewSeasonName("");

            const resetToday = new Date();
            resetToday.setHours(12, 0, 0, 0);

            const resetNextWeek = new Date(resetToday);
            resetNextWeek.setDate(resetToday.getDate() + 7);

            setNewSeasonStartDate(resetToday);
            setNewSeasonEndDate(resetNextWeek);
          }
        }}
      >
        <Drawer.Content className="flex flex-col">
          <Drawer.Header className="flex-shrink-0">
            <Heading level="h2" className="text-l font-semibold">
              Add Seasonal Period
            </Heading>
          </Drawer.Header>
          <Drawer.Body className="flex-1 overflow-y-auto">
            <div className=" space-y-6">
              <div>
                <Label htmlFor="seasonName" className="mb-1 block">
                  Season Name
                </Label>
                <Input
                  id="seasonName"
                  value={newSeasonName}
                  onChange={(e) => setNewSeasonName(e.target.value)}
                  placeholder="e.g., Summer 2023"
                  className="w-full"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startDate" className="mb-1 block">
                    Start Date
                  </Label>
                  <div className="relative">
                    <Input
                      id="startDate"
                      type="date"
                      value={
                        newSeasonStartDate
                          ? format(newSeasonStartDate, "yyyy-MM-dd")
                          : ""
                      }
                      onChange={(e) => {
                        if (e.target.value) {
                          // Create date at noon to avoid timezone issues
                          const date = new Date(e.target.value + "T12:00:00");
                          setNewSeasonStartDate(date);
                        } else {
                          setNewSeasonStartDate(new Date());
                        }
                      }}
                      className="w-full"
                      required
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="endDate" className="mb-1 block">
                    End Date
                  </Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                    </div>
                    <Input
                      id="endDate"
                      type="date"
                      value={
                        newSeasonEndDate
                          ? format(newSeasonEndDate, "yyyy-MM-dd")
                          : ""
                      }
                      onChange={(e) => {
                        if (e.target.value) {
                          // Create date at noon to avoid timezone issues
                          const date = new Date(e.target.value + "T12:00:00");
                          setNewSeasonEndDate(date);
                        } else {
                          // Default to 7 days after start date
                          const date = new Date(newSeasonStartDate);
                          date.setDate(date.getDate() + 7);
                          setNewSeasonEndDate(date);
                        }
                      }}
                      className="w-full"
                      required
                    />
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 dark:bg-blue-950/30 p-4 rounded-md border border-blue-200 dark:border-blue-800">
                <Text className="text-blue-800 dark:text-blue-200">
                  <strong>Note:</strong> Adding a new seasonal period will
                  create pricing rows for all room types, occupancy types, and
                  meal plans. Base prices will be copied to the new seasonal
                  period by default.
                </Text>
              </div>
            </div>
          </Drawer.Body>

          {/* Fixed Footer with CTAs */}
          <div className="flex-shrink-0 border-t border-border bg-card p-4">
            <div className="flex justify-end gap-4">
              <Button
                variant="secondary"
                onClick={() => setIsSeasonalModalOpen(false)}
                disabled={isAddingSeasonalPeriod}
                className="flex-1 sm:flex-none"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleAddSeasonalPeriod}
                disabled={isAddingSeasonalPeriod}
                className="flex-1 sm:flex-none"
              >
                {isAddingSeasonalPeriod ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    Adding...
                  </>
                ) : (
                  "Add Season"
                )}
              </Button>
            </div>
          </div>
        </Drawer.Content>
      </Drawer>

      {/* Bulk Price Update Modal */}
      <BulkPriceUpdateModal
        isOpen={isBulkUpdateModalOpen}
        onClose={() => setIsBulkUpdateModalOpen(false)}
        roomConfigs={roomConfigs}
        occupancyConfigs={occupancyConfigs}
        mealPlans={mealPlans}
        seasonalPeriods={seasonalPeriods}
        currencyCode={currencyCode}
        pricingRows={pricingRows}
        onApplyUpdate={handleBulkPriceUpdate}
        onCurrencyChange={handleCurrencyChange}
      />

      {/* Compact Filters */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6 p-4 bg-muted/50 rounded-lg border border-border">
        {/* Seasons Filter using @camped-ai/ui Select */}
        <div>
          <Label
            htmlFor="showAllSeasons"
            className="mb-1 block text-sm font-medium"
          >
            Season
          </Label>
          <Select
            value={selectedSeasonFilter}
            onValueChange={setSelectedSeasonFilter}
          >
            <Select.Trigger id="showAllSeasons" className="w-full">
              <Select.Value placeholder="All Seasons" />
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="all">All Seasons</Select.Item>
              <Select.Item value="base">Base Pricing Only</Select.Item>
              {seasonalPeriods.map((season) => (
                <Select.Item key={season.id} value={season.id}>
                  {season.name}
                </Select.Item>
              ))}
            </Select.Content>
          </Select>
        </div>

        <FilterSelect
          id="roomConfig"
          label="Room Type"
          value={selectedRoomConfig}
          onChange={setSelectedRoomConfig}
          options={roomConfigs}
          placeholder="All Room Types"
        />

        <FilterSelect
          id="occupancyConfig"
          label="Occupancy Type"
          value={selectedOccupancyConfig}
          onChange={setSelectedOccupancyConfig}
          options={occupancyConfigs}
          placeholder="All Occupancy Types"
        />

        <FilterSelect
          id="mealPlan"
          label="Meal Plan"
          value={selectedMealPlan}
          onChange={setSelectedMealPlan}
          options={mealPlans}
          placeholder="All Meal Plans"
        />
      </div>

      <div className="overflow-x-auto rounded-lg border border-border shadow-sm">
        <table className="min-w-full divide-y divide-border table-fixed">
          <thead className="bg-muted/50">
            <tr>
              <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-32">
                Season
              </th>
              <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-32">
                Room Type
              </th>
              <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-20">
                Occupancy
              </th>
              <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-28">
                Meal Plan
              </th>

              {/* Cost & Margin Management Columns */}
              <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide w-24">
                <div className="flex flex-col items-center gap-0.5">
                  <span className="font-semibold">Gross Cost</span>
                  <span className="text-xs text-muted-foreground/70 font-normal">
                    {getCurrencySymbol()}
                  </span>
                </div>
              </th>
              <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide w-24">
                <div className="flex flex-col items-center gap-0.5">
                  <span className="font-semibold">Fixed Margin</span>
                  <span className="text-xs text-muted-foreground/70 font-normal">
                    {getCurrencySymbol()}
                  </span>
                </div>
              </th>
              <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide w-20">
                <div className="flex flex-col items-center gap-0.5">
                  <span className="font-semibold">Margin %</span>
                </div>
              </th>
              <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide w-24">
                <div className="flex flex-col items-center gap-0.5">
                  <span className="font-semibold">Total</span>
                  <span className="text-xs text-muted-foreground/70 font-normal">
                    {getCurrencySymbol()}
                  </span>
                </div>
              </th>
              <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide w-20">
                Actions
              </th>

              {/* Grouped Weekday Columns */}
              {showAllDays ? (
                <>
                  {weekdays.map((day, index) => (
                    <th
                      key={day.id}
                      className={`px-1 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide ${
                        index === 0
                          ? "border-l-2 border-l-blue-400"
                          : "border-l-2 border-l-blue-300"
                      } ${
                        index === weekdays.length - 1
                          ? "border-r-2 border-r-blue-400"
                          : ""
                      } bg-blue-50/50 dark:bg-blue-950/20`}
                      colSpan={showWeekdayCostMargin ? 4 : 1}
                    >
                      <div className="flex flex-col items-center gap-0.5">
                        <span className="font-semibold text-blue-600 dark:text-blue-400">
                          {day.name}
                        </span>
                        {showWeekdayCostMargin ? (
                          <div className="flex items-center justify-between gap-1 text-xs w-full px-1">
                            <span className="text-center flex-1">Cost</span>
                            <span className="text-center flex-1">Margin</span>
                            <span className="text-center flex-1">%</span>
                            <span className="text-center flex-1">Price</span>
                          </div>
                        ) : (
                          <span className="text-xs text-muted-foreground/70 font-normal">
                            {getCurrencySymbol()}
                          </span>
                        )}
                      </div>
                    </th>
                  ))}
                </>
              ) : (
                <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide">
                  <div className="flex flex-col items-center gap-0.5">
                    <span className="font-semibold">Price</span>
                    <span className="text-xs text-muted-foreground/70 font-normal">
                      {getCurrencySymbol()}
                    </span>
                  </div>
                </th>
              )}
              {/* <th className="px-2 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wide">
                Actions
              </th> */}
            </tr>
          </thead>
          <tbody className="bg-background divide-y divide-border">
            {filteredPricingRows.map((row, index) => {
              // Find the room, occupancy, and meal plan objects
              const room = roomConfigs.find((r) => r.id === row.roomConfigId);
              const occupancy = occupancyConfigs.find(
                (o) => o.id === row.occupancyTypeId
              );
              const mealPlan = mealPlans.find((m) => m.id === row.mealPlanId);
              const seasonalPeriod = row.seasonalPeriodId
                ? seasonalPeriods.find((s) => s.id === row.seasonalPeriodId)
                : undefined;

              return (
                <tr
                  key={row.id}
                  className={index % 2 === 0 ? "bg-background" : "bg-muted/50"}
                >
                  <td className="px-3 py-3 whitespace-nowrap text-sm w-32">
                    {seasonalPeriod ? (
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                        <div className="flex flex-col min-w-0">
                          <span
                            className="font-medium truncate text-xs"
                            title={seasonalPeriod.name}
                          >
                            {seasonalPeriod.name}
                          </span>
                          <span className="text-xs text-muted-foreground truncate">
                            {format(
                              new Date(seasonalPeriod.start_date),
                              "MMM d"
                            )}{" "}
                            -{" "}
                            {format(new Date(seasonalPeriod.end_date), "MMM d")}
                          </span>
                        </div>
                      </div>
                    ) : (
                      <span className="font-medium text-foreground text-xs">
                        Base Price {row.id}
                      </span>
                    )}
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap text-sm w-32">
                    <div
                      className="truncate text-xs"
                      title={room?.title || "Unknown Room"}
                    >
                      <span className="font-medium">
                        {room?.title || "Unknown Room"}
                      </span>
                    </div>
                  </td>
                  <td className="px-2 py-3 whitespace-nowrap text-sm w-20">
                    <div
                      className="truncate text-xs"
                      title={occupancy?.name || "Unknown Occupancy"}
                    >
                      {occupancy?.name || "Unknown Occupancy"}
                    </div>
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap text-sm w-28">
                    <div
                      className="truncate"
                      title={
                        (occupancy as any)?.type === "EXTRA_BED" ||
                        occupancy?.name?.toLowerCase().includes("extra bed") ||
                        (occupancy as any)?.type === "COT" ||
                        occupancy?.name?.toLowerCase().includes("cot")
                          ? "-"
                          : mealPlan?.name || "Unknown Meal Plan"
                      }
                    >
                      {(occupancy as any)?.type === "EXTRA_BED" ||
                      occupancy?.name?.toLowerCase().includes("extra bed") ||
                      (occupancy as any)?.type === "COT" ||
                      occupancy?.name?.toLowerCase().includes("cot") ? (
                        <span className="text-muted-foreground">-</span>
                      ) : (
                        mealPlan?.name || "Unknown Meal Plan"
                      )}
                    </div>
                  </td>

                  {/* Cost & Margin Management Columns */}
                  {/* Gross Cost */}
                  <td className="px-2 py-3 whitespace-nowrap">
                    <div className="flex justify-center">
                      <Input
                        type="number"
                        className={`w-20 py-1 text-sm border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                          row.modified
                            ? "border-primary bg-primary/10 dark:bg-primary/20"
                            : "border-input"
                        }`}
                        value={row.defaultValues.grossCost}
                        onChange={(e) =>
                          handleDefaultValueChange(
                            row.roomConfigId,
                            row.occupancyTypeId,
                            row.mealPlanId,
                            row.seasonalPeriodId,
                            "grossCost",
                            parseFloat(e.target.value) || 0
                          )
                        }
                        min="0"
                        step="0.01"
                        placeholder="0.00"
                      />
                    </div>
                  </td>

                  {/* Fixed Margin */}
                  <td className="px-2 py-3 whitespace-nowrap">
                    <div className="flex justify-center">
                      <Input
                        type="number"
                        className={`w-20 py-1 text-sm border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                          row.modified
                            ? "border-primary bg-primary/10 dark:bg-primary/20"
                            : "border-input"
                        }`}
                        value={row.defaultValues.fixedMargin}
                        onChange={(e) =>
                          handleDefaultValueChange(
                            row.roomConfigId,
                            row.occupancyTypeId,
                            row.mealPlanId,
                            row.seasonalPeriodId,
                            "fixedMargin",
                            parseFloat(e.target.value) || 0
                          )
                        }
                        step="0.01"
                        placeholder="0.00"
                      />
                    </div>
                  </td>

                  {/* Margin Percentage */}
                  <td className="px-2 py-3 whitespace-nowrap">
                    <div className="flex justify-center">
                      <Input
                        type="number"
                        className={`w-16 py-1 text-sm border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                          row.modified
                            ? "border-primary bg-primary/10 dark:bg-primary/20"
                            : "border-input"
                        }`}
                        value={row.defaultValues.marginPercentage}
                        onChange={(e) =>
                          handleDefaultValueChange(
                            row.roomConfigId,
                            row.occupancyTypeId,
                            row.mealPlanId,
                            row.seasonalPeriodId,
                            "marginPercentage",
                            parseFloat(e.target.value) || 0
                          )
                        }
                        min="0"
                        max="1000"
                        step="0.1"
                        placeholder="0"
                      />
                    </div>
                  </td>

                  {/* Calculated Total */}
                  <td className="px-2 py-3 whitespace-nowrap">
                    <div className="flex justify-center">
                      <div
                        className={`w-20 py-1 px-2 text-sm rounded text-center font-medium ${
                          row.defaultValues.total > 0
                            ? "bg-green-50 text-green-700 border border-green-200"
                            : "bg-gray-50 text-gray-500 border border-gray-200"
                        }`}
                      >
                        {row.defaultValues.total.toFixed(2)}
                      </div>
                    </div>
                  </td>

                  {/* Apply to All Days Action */}
                  <td className="px-2 py-3 whitespace-nowrap">
                    <div className="flex justify-center">
                      <Button
                        variant="secondary"
                        size="small"
                        onClick={() =>
                          handleApplyToAllDays(
                            row.roomConfigId,
                            row.occupancyTypeId,
                            row.mealPlanId,
                            row.seasonalPeriodId
                          )
                        }
                        disabled={
                          !row.defaultValues.grossCost ||
                          row.defaultValues.grossCost <= 0
                        }
                        className="flex items-center gap-1 text-xs px-2 py-1"
                        title="Apply default cost and margin values to all days of the week"
                      >
                        <Copy className="w-3 h-3" />
                        Apply
                      </Button>
                    </div>
                  </td>

                  {/* Grouped Weekday Cells */}
                  {showAllDays ? (
                    <>
                      {weekdays.map((day, dayIndex) => (
                        <React.Fragment key={`${day.id}-grouped`}>
                          {showWeekdayCostMargin ? (
                            <>
                              {/* Gross Cost */}
                              <td
                                className={`px-1 py-3 whitespace-nowrap ${
                                  dayIndex === 0
                                    ? "border-l-2 border-l-blue-400"
                                    : "border-l-2 border-l-blue-300"
                                } bg-blue-50/30 dark:bg-blue-950/10`}
                              >
                                <div className="flex justify-center">
                                  <Input
                                    type="number"
                                    className={`w-16 py-1 text-xs border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                                      row.modified
                                        ? "border-primary bg-primary/10 dark:bg-primary/20"
                                        : "border-input"
                                    }`}
                                    value={
                                      row.weekdayValues[
                                        day.id as keyof typeof row.weekdayValues
                                      ].grossCost
                                    }
                                    onChange={(e) =>
                                      handleWeekdayValueChange(
                                        row.roomConfigId,
                                        row.occupancyTypeId,
                                        row.mealPlanId,
                                        row.seasonalPeriodId,
                                        day.id as
                                          | "mon"
                                          | "tue"
                                          | "wed"
                                          | "thu"
                                          | "fri"
                                          | "sat"
                                          | "sun",
                                        "grossCost",
                                        parseFloat(e.target.value) || 0
                                      )
                                    }
                                    min="0"
                                    step="0.01"
                                    placeholder="0.00"
                                  />
                                </div>
                              </td>

                              {/* Fixed Margin */}
                              <td className="px-1 py-3 whitespace-nowrap bg-blue-50/30 dark:bg-blue-950/10">
                                <div className="flex justify-center">
                                  <Input
                                    type="number"
                                    className={`w-16 py-1 text-xs border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                                      row.modified
                                        ? "border-primary bg-primary/10 dark:bg-primary/20"
                                        : "border-input"
                                    }`}
                                    value={
                                      row.weekdayValues[
                                        day.id as keyof typeof row.weekdayValues
                                      ].fixedMargin
                                    }
                                    onChange={(e) =>
                                      handleWeekdayValueChange(
                                        row.roomConfigId,
                                        row.occupancyTypeId,
                                        row.mealPlanId,
                                        row.seasonalPeriodId,
                                        day.id as
                                          | "mon"
                                          | "tue"
                                          | "wed"
                                          | "thu"
                                          | "fri"
                                          | "sat"
                                          | "sun",
                                        "fixedMargin",
                                        parseFloat(e.target.value) || 0
                                      )
                                    }
                                    step="0.01"
                                    placeholder="0.00"
                                  />
                                </div>
                              </td>

                              {/* Margin Percentage */}
                              <td className="px-1 py-3 whitespace-nowrap bg-blue-50/30 dark:bg-blue-950/10">
                                <div className="flex justify-center">
                                  <Input
                                    type="number"
                                    className={`w-12 py-1 text-xs border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                                      row.modified
                                        ? "border-primary bg-primary/10 dark:bg-primary/20"
                                        : "border-input"
                                    }`}
                                    value={
                                      row.weekdayValues[
                                        day.id as keyof typeof row.weekdayValues
                                      ].marginPercentage
                                    }
                                    onChange={(e) =>
                                      handleWeekdayValueChange(
                                        row.roomConfigId,
                                        row.occupancyTypeId,
                                        row.mealPlanId,
                                        row.seasonalPeriodId,
                                        day.id as
                                          | "mon"
                                          | "tue"
                                          | "wed"
                                          | "thu"
                                          | "fri"
                                          | "sat"
                                          | "sun",
                                        "marginPercentage",
                                        parseFloat(e.target.value) || 0
                                      )
                                    }
                                    min="0"
                                    max="1000"
                                    step="0.1"
                                    placeholder="0"
                                  />
                                </div>
                              </td>

                              {/* Final Price */}
                              <td
                                className={`px-1 py-3 whitespace-nowrap bg-blue-50/30 dark:bg-blue-950/10 ${
                                  dayIndex === weekdays.length - 1
                                    ? "border-r-2 border-r-blue-400"
                                    : ""
                                }`}
                              >
                                <div className="flex justify-center">
                                  <Input
                                    type="number"
                                    className={`w-16 py-1 text-xs border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                                      row.modified
                                        ? "border-primary bg-primary/10 dark:bg-primary/20"
                                        : "border-input"
                                    }`}
                                    value={formatPrice(
                                      row.prices[
                                        day.id as keyof typeof row.prices
                                      ]
                                    )}
                                    onChange={(e) =>
                                      handlePriceChange(
                                        row.roomConfigId,
                                        row.occupancyTypeId,
                                        row.mealPlanId,
                                        row.seasonalPeriodId,
                                        day.id,
                                        parsePrice(e.target.value)
                                      )
                                    }
                                    min="0"
                                    step="0.01"
                                  />
                                </div>
                              </td>
                            </>
                          ) : (
                            /* Simple Price Column */
                            <td
                              className={`px-2 py-3 whitespace-nowrap ${
                                dayIndex === 0
                                  ? "border-l-2 border-l-blue-400"
                                  : "border-l-2 border-l-blue-300"
                              } ${
                                dayIndex === weekdays.length - 1
                                  ? "border-r-2 border-r-blue-400"
                                  : ""
                              } bg-blue-50/30 dark:bg-blue-950/10`}
                            >
                              <div className="flex justify-center">
                                <Input
                                  type="number"
                                  className={`w-20 py-1 text-sm border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                                    row.modified
                                      ? "border-primary bg-primary/10 dark:bg-primary/20"
                                      : "border-input"
                                  }`}
                                  value={formatPrice(
                                    row.prices[
                                      day.id as keyof typeof row.prices
                                    ]
                                  )}
                                  onChange={(e) =>
                                    handlePriceChange(
                                      row.roomConfigId,
                                      row.occupancyTypeId,
                                      row.mealPlanId,
                                      row.seasonalPeriodId,
                                      day.id,
                                      parsePrice(e.target.value)
                                    )
                                  }
                                  min="0"
                                  step="0.01"
                                />
                              </div>
                            </td>
                          )}
                        </React.Fragment>
                      ))}
                    </>
                  ) : (
                    <td className="px-3 py-3 whitespace-nowrap">
                      <div className="flex justify-center">
                        <div className="relative">
                          {/* <div className="absolute inset-y-0 left-0 left-0 flex items-center pl-2 pointer-events-none">
                            <span className="text-muted-foreground text-xs">
                              {getCurrencySymbol()}
                            </span>
                          </div> */}
                          <Input
                            type="number"
                            className={`w-24 pl-5 py-1 text-sm border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                              row.modified
                                ? "border-primary bg-primary/10 dark:bg-primary/20"
                                : "border-input"
                            }`}
                            value={formatPrice(row.prices.mon)}
                            onChange={(e) => {
                              const value = parsePrice(e.target.value);
                              // First update Monday price
                              handlePriceChange(
                                row.roomConfigId,
                                row.occupancyTypeId,
                                row.mealPlanId,
                                row.seasonalPeriodId,
                                "mon",
                                value
                              );
                              // Then copy to all days
                              setTimeout(() => {
                                handleCopyToAllDays(
                                  row.roomConfigId,
                                  row.occupancyTypeId,
                                  row.mealPlanId,
                                  row.seasonalPeriodId,
                                  "mon"
                                );
                              }, 0);
                            }}
                            min="0"
                            step="0.01"
                          />
                        </div>
                      </div>
                    </td>
                  )}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </Container>
  );
};

export default ComprehensivePricingTable;
