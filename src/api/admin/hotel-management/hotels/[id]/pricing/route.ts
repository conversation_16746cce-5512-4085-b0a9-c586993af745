import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { HOTEL_PRICING_MODULE } from "../../../../../../modules/hotel-management/hotel-pricing";

// GET endpoint to retrieve all comprehensive pricing data for a hotel
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;
    const query = req.scope.resolve("query");
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    console.log(
      `[Pricing API] Fetching comprehensive pricing for hotel: ${hotelId}`
    );

    // Get hotel details
    const { data: hotel } = await query.graph({
      entity: "hotel",
      filters: {
        id: [hotelId],
      },
      fields: ["id", "name", "handle", "category_id"],
    });

    console.log(`[Pricing API] Hotel query result:`, hotel);

    if (!hotel || hotel.length === 0) {
      return res.status(404).json({ message: "Hotel not found" });
    }

    // Get all room configurations for this hotel
    // Try multiple methods to find room configurations
    let roomConfigs = [];

    console.log(`[Pricing API] Hotel category_id: ${hotel[0].category_id}`);

    // Method 1: Try using hotel's category_id if available
    if (hotel[0].category_id) {
      try {
        console.log(
          `[Pricing API] Trying method 1: category_id = ${hotel[0].category_id}`
        );
        const { data: categoryRoomConfigs } = await query.graph({
          entity: "product",
          filters: {
            categories: { id: [hotel[0].category_id] },
            // Filter for products that have price_set_id in metadata (room configs)
            metadata: {
              price_set_id: { exists: true },
            },
          },
          fields: ["id", "title", "handle", "description", "metadata"],
        });

        console.log(
          `[Pricing API] Method 1 found ${
            categoryRoomConfigs?.length || 0
          } room configs`
        );
        if (categoryRoomConfigs && categoryRoomConfigs.length > 0) {
          roomConfigs = categoryRoomConfigs;
        }
      } catch (error) {
        console.error(
          "[Pricing API] Error getting room configs by category_id:",
          error
        );
      }
    }

    // Method 2: If no room configs found, try using hotel_id in metadata
    if (roomConfigs.length === 0) {
      try {
        console.log(
          `[Pricing API] Trying method 2: hotel_id in metadata = ${hotelId}`
        );
        const { data: metadataRoomConfigs } = await query.graph({
          entity: "product",
          filters: {
            metadata: {
              hotel_id: hotelId,
              price_set_id: { exists: true },
            },
          },
          fields: ["id", "title", "handle", "description", "metadata"],
        });

        console.log(
          `[Pricing API] Method 2 found ${
            metadataRoomConfigs?.length || 0
          } room configs`
        );
        if (metadataRoomConfigs && metadataRoomConfigs.length > 0) {
          roomConfigs = metadataRoomConfigs;
        }
      } catch (error) {
        console.error(
          "[Pricing API] Error getting room configs by hotel_id in metadata:",
          error
        );
      }
    }

    // Method 3: If still no room configs found, try using categories with hotelId directly
    if (roomConfigs.length === 0) {
      try {
        console.log(
          `[Pricing API] Trying method 3: categories with hotelId = ${hotelId}`
        );
        const { data: directRoomConfigs } = await query.graph({
          entity: "product",
          filters: {
            categories: { id: [hotelId] },
            // Filter for products that have price_set_id in metadata (room configs)
            metadata: {
              price_set_id: { exists: true },
            },
          },
          fields: ["id", "title", "handle", "description", "metadata"],
        });

        console.log(
          `[Pricing API] Method 3 found ${
            directRoomConfigs?.length || 0
          } room configs`
        );
        if (directRoomConfigs && directRoomConfigs.length > 0) {
          roomConfigs = directRoomConfigs;
        }
      } catch (error) {
        console.error(
          "[Pricing API] Error getting room configs by direct categories:",
          error
        );
      }
    }

    console.log(
      `[Pricing API] Final room configs count: ${roomConfigs.length}`
    );

    // Method 4: If still no room configs found, try without price_set_id filter
    if (roomConfigs.length === 0) {
      try {
        console.log(
          `[Pricing API] Trying method 4: without price_set_id filter`
        );
        const { data: allRoomConfigs } = await query.graph({
          entity: "product",
          filters: {
            categories: { id: [hotel[0].category_id] },
          },
          fields: ["id", "title", "handle", "description", "metadata"],
        });

        console.log(
          `[Pricing API] Method 4 found ${allRoomConfigs?.length || 0} products`
        );
        if (allRoomConfigs && allRoomConfigs.length > 0) {
          // Filter for room configs manually
          const filteredRoomConfigs = allRoomConfigs.filter(
            (product) =>
              product.metadata &&
              (product.metadata.price_set_id ||
                product.metadata.hotel_id === hotelId ||
                product.metadata.type === "room_config")
          );
          console.log(
            `[Pricing API] Method 4 filtered to ${filteredRoomConfigs.length} room configs`
          );
          roomConfigs = filteredRoomConfigs;
        }
      } catch (error) {
        console.error("[Pricing API] Error in method 4:", error);
      }
    }

    // Method 5: If still no room configs, try getting all products and filter by hotel_id in metadata
    if (roomConfigs.length === 0) {
      try {
        console.log(
          `[Pricing API] Trying method 5: all products with hotel_id filter`
        );
        const { data: allProducts } = await query.graph({
          entity: "product",
          fields: ["id", "title", "handle", "description", "metadata"],
        });

        console.log(
          `[Pricing API] Method 5 found ${
            allProducts?.length || 0
          } total products`
        );
        if (allProducts && allProducts.length > 0) {
          // Filter for this hotel's room configs
          const hotelRoomConfigs = allProducts.filter(
            (product) =>
              product.metadata && product.metadata.hotel_id === hotelId
          );
          console.log(
            `[Pricing API] Method 5 filtered to ${hotelRoomConfigs.length} room configs for this hotel`
          );
          roomConfigs = hotelRoomConfigs;
        }
      } catch (error) {
        console.error("[Pricing API] Error in method 5:", error);
      }
    }

    // Method 6: Use product module service directly (like other working endpoints)
    if (roomConfigs.length === 0) {
      try {
        console.log(`[Pricing API] Trying method 6: product module service`);
        const productModuleService = req.scope.resolve(Modules.PRODUCT);

        const result = await productModuleService.listProducts({
          is_giftcard: false,
        });

        console.log(
          `[Pricing API] Method 6 found ${result?.length || 0} total products`
        );

        if (result && result.length > 0) {
          // Filter products by metadata.hotel_id
          const hotelRoomConfigs = result.filter((product) => {
            return (
              product.metadata &&
              product.metadata.hotel_id === hotelId &&
              !product.metadata.add_on_service
            ); // Exclude add-on services
          });

          console.log(
            `[Pricing API] Method 6 filtered to ${hotelRoomConfigs.length} room configs for this hotel`
          );
          roomConfigs = hotelRoomConfigs;
        }
      } catch (error) {
        console.error("[Pricing API] Error in method 6:", error);
      }
    }

    console.log(
      `[Pricing API] Final room configs count after all methods: ${roomConfigs.length}`
    );

    // Get occupancy configurations for this hotel
    const occupancyConfigs = await hotelPricingService.listOccupancyConfigs({
      hotel_id: hotelId,
    });

    // Get meal plans for this hotel
    const mealPlans = await hotelPricingService.listMealPlans({
      hotel_id: hotelId,
    });

    // Get comprehensive pricing data for all room configurations
    const roomPricingData = await Promise.all(
      roomConfigs.map(async (roomConfig) => {
        try {
          // Get base price rules for this room configuration
          const basePriceRules = await hotelPricingService.listBasePriceRules({
            room_config_id: roomConfig.id,
          });

          // Format weekday pricing rules
          const weekdayRules = await Promise.all(
            basePriceRules.map(async (rule) => {
              try {
                // Get the occupancy type and meal plan details
                const occupancyConfig =
                  await hotelPricingService.retrieveOccupancyConfig(
                    rule.occupancy_type_id
                  );
                let mealPlan = null;
                if (rule.meal_plan_id) {
                  mealPlan = await hotelPricingService.retrieveMealPlan(
                    rule.meal_plan_id
                  );
                }

                return {
                  id: rule.id,
                  occupancy_type_id: rule.occupancy_type_id,
                  meal_plan_id: rule.meal_plan_id,
                  amount: rule.amount,
                  room_config_id: rule.room_config_id,
                  weekday_prices: {
                    mon: rule.monday_price || rule.amount,
                    tue: rule.tuesday_price || rule.amount,
                    wed: rule.wednesday_price || rule.amount,
                    thu: rule.thursday_price || rule.amount,
                    fri: rule.friday_price || rule.amount,
                    sat: rule.saturday_price || rule.amount,
                    sun: rule.sunday_price || rule.amount,
                  },
                  // Include default cost and margin values (convert from cents to display units)
                  default_values: {
                    gross_cost: rule.default_gross_cost ? rule.default_gross_cost / 100 : null,
                    fixed_margin: rule.default_fixed_margin ? rule.default_fixed_margin / 100 : null,
                    margin_percentage: rule.default_margin_percentage,
                    total: rule.default_total ? rule.default_total / 100 : null,
                  },
                  // Day-specific cost and margin data
                  cost_margin_data: {
                    mon: {
                      gross_cost: rule.monday_gross_cost,
                      fixed_margin: rule.monday_fixed_margin,
                      margin_percentage: rule.monday_margin_percentage,
                    },
                    tue: {
                      gross_cost: rule.tuesday_gross_cost,
                      fixed_margin: rule.tuesday_fixed_margin,
                      margin_percentage: rule.tuesday_margin_percentage,
                    },
                    wed: {
                      gross_cost: rule.wednesday_gross_cost,
                      fixed_margin: rule.wednesday_fixed_margin,
                      margin_percentage: rule.wednesday_margin_percentage,
                    },
                    thu: {
                      gross_cost: rule.thursday_gross_cost,
                      fixed_margin: rule.thursday_fixed_margin,
                      margin_percentage: rule.thursday_margin_percentage,
                    },
                    fri: {
                      gross_cost: rule.friday_gross_cost,
                      fixed_margin: rule.friday_fixed_margin,
                      margin_percentage: rule.friday_margin_percentage,
                    },
                    sat: {
                      gross_cost: rule.saturday_gross_cost,
                      fixed_margin: rule.saturday_fixed_margin,
                      margin_percentage: rule.saturday_margin_percentage,
                    },
                    sun: {
                      gross_cost: rule.sunday_gross_cost,
                      fixed_margin: rule.sunday_fixed_margin,
                      margin_percentage: rule.sunday_margin_percentage,
                    },
                  },
                  currency_code: rule.currency_code,
                  occupancy_type: occupancyConfig,
                  meal_plan: mealPlan,
                  created_at: rule.created_at,
                  updated_at: rule.updated_at,
                };
              } catch (error) {
                console.error(
                  `Error fetching details for rule ${rule.id}:`,
                  error
                );
                return {
                  id: rule.id,
                  occupancy_type_id: rule.occupancy_type_id,
                  meal_plan_id: rule.meal_plan_id,
                  amount: rule.amount,
                  room_config_id: rule.room_config_id,
                  weekday_prices: {
                    mon: rule.monday_price || rule.amount,
                    tue: rule.tuesday_price || rule.amount,
                    wed: rule.wednesday_price || rule.amount,
                    thu: rule.thursday_price || rule.amount,
                    fri: rule.friday_price || rule.amount,
                    sat: rule.saturday_price || rule.amount,
                    sun: rule.sunday_price || rule.amount,
                  },
                  // Include default cost and margin values (convert from cents to display units)
                  default_values: {
                    gross_cost: rule.default_gross_cost ? rule.default_gross_cost / 100 : null,
                    fixed_margin: rule.default_fixed_margin ? rule.default_fixed_margin / 100 : null,
                    margin_percentage: rule.default_margin_percentage,
                    total: rule.default_total ? rule.default_total / 100 : null,
                  },
                  // Day-specific cost and margin data (legacy format)
                  cost_margin_data: {
                    mon: {
                      gross_cost: (() => {
                        const rawValue = rule.monday_gross_cost;
                        const convertedValue = rawValue ? rawValue / 100 : null;
                        console.log(`[GET API] 🔍 Monday gross_cost: raw=${rawValue}, converted=${convertedValue}`);
                        return convertedValue;
                      })(),
                      fixed_margin: (() => {
                        const rawValue = rule.monday_fixed_margin;
                        const convertedValue = rawValue ? rawValue / 100 : null;
                        console.log(`[GET API] 🔍 Monday fixed_margin: raw=${rawValue}, converted=${convertedValue}`);
                        return convertedValue;
                      })(),
                      margin_percentage: (() => {
                        const rawValue = rule.monday_margin_percentage;
                        console.log(`[GET API] 🔍 Monday margin_percentage: raw=${rawValue}`);
                        return rawValue;
                      })(),
                    },
                    tue: {
                      gross_cost: rule.tuesday_gross_cost !== null && rule.tuesday_gross_cost !== undefined ? rule.tuesday_gross_cost / 100 : null,
                      fixed_margin: rule.tuesday_fixed_margin !== null && rule.tuesday_fixed_margin !== undefined ? rule.tuesday_fixed_margin / 100 : null,
                      margin_percentage: rule.tuesday_margin_percentage !== null && rule.tuesday_margin_percentage !== undefined ? rule.tuesday_margin_percentage : null,
                    },
                    wed: {
                      gross_cost: rule.wednesday_gross_cost !== null && rule.wednesday_gross_cost !== undefined ? rule.wednesday_gross_cost / 100 : null,
                      fixed_margin: rule.wednesday_fixed_margin !== null && rule.wednesday_fixed_margin !== undefined ? rule.wednesday_fixed_margin / 100 : null,
                      margin_percentage: rule.wednesday_margin_percentage !== null && rule.wednesday_margin_percentage !== undefined ? rule.wednesday_margin_percentage : null,
                    },
                    thu: {
                      gross_cost: rule.thursday_gross_cost !== null && rule.thursday_gross_cost !== undefined ? rule.thursday_gross_cost / 100 : null,
                      fixed_margin: rule.thursday_fixed_margin !== null && rule.thursday_fixed_margin !== undefined ? rule.thursday_fixed_margin / 100 : null,
                      margin_percentage: rule.thursday_margin_percentage !== null && rule.thursday_margin_percentage !== undefined ? rule.thursday_margin_percentage : null,
                    },
                    fri: {
                      gross_cost: rule.friday_gross_cost !== null && rule.friday_gross_cost !== undefined ? rule.friday_gross_cost / 100 : null,
                      fixed_margin: rule.friday_fixed_margin !== null && rule.friday_fixed_margin !== undefined ? rule.friday_fixed_margin / 100 : null,
                      margin_percentage: rule.friday_margin_percentage !== null && rule.friday_margin_percentage !== undefined ? rule.friday_margin_percentage : null,
                    },
                    sat: {
                      gross_cost: rule.saturday_gross_cost !== null && rule.saturday_gross_cost !== undefined ? rule.saturday_gross_cost / 100 : null,
                      fixed_margin: rule.saturday_fixed_margin !== null && rule.saturday_fixed_margin !== undefined ? rule.saturday_fixed_margin / 100 : null,
                      margin_percentage: rule.saturday_margin_percentage !== null && rule.saturday_margin_percentage !== undefined ? rule.saturday_margin_percentage : null,
                    },
                    sun: {
                      gross_cost: rule.sunday_gross_cost !== null && rule.sunday_gross_cost !== undefined ? rule.sunday_gross_cost / 100 : null,
                      fixed_margin: rule.sunday_fixed_margin !== null && rule.sunday_fixed_margin !== undefined ? rule.sunday_fixed_margin / 100 : null,
                      margin_percentage: rule.sunday_margin_percentage !== null && rule.sunday_margin_percentage !== undefined ? rule.sunday_margin_percentage : null,
                    },
                  },
                  // New weekday_values structure (includes total for each day)
                  weekday_values: {
                    mon: {
                      gross_cost: rule.monday_gross_cost !== null && rule.monday_gross_cost !== undefined ? rule.monday_gross_cost / 100 : null,
                      fixed_margin: rule.monday_fixed_margin !== null && rule.monday_fixed_margin !== undefined ? rule.monday_fixed_margin / 100 : null,
                      margin_percentage: rule.monday_margin_percentage !== null && rule.monday_margin_percentage !== undefined ? rule.monday_margin_percentage : null,
                      total: rule.monday_price !== null && rule.monday_price !== undefined ? rule.monday_price / 100 : null,
                    },
                    tue: {
                      gross_cost: rule.tuesday_gross_cost !== null && rule.tuesday_gross_cost !== undefined ? rule.tuesday_gross_cost / 100 : null,
                      fixed_margin: rule.tuesday_fixed_margin !== null && rule.tuesday_fixed_margin !== undefined ? rule.tuesday_fixed_margin / 100 : null,
                      margin_percentage: rule.tuesday_margin_percentage !== null && rule.tuesday_margin_percentage !== undefined ? rule.tuesday_margin_percentage : null,
                      total: rule.tuesday_price !== null && rule.tuesday_price !== undefined ? rule.tuesday_price / 100 : null,
                    },
                    wed: {
                      gross_cost: rule.wednesday_gross_cost !== null && rule.wednesday_gross_cost !== undefined ? rule.wednesday_gross_cost / 100 : null,
                      fixed_margin: rule.wednesday_fixed_margin !== null && rule.wednesday_fixed_margin !== undefined ? rule.wednesday_fixed_margin / 100 : null,
                      margin_percentage: rule.wednesday_margin_percentage !== null && rule.wednesday_margin_percentage !== undefined ? rule.wednesday_margin_percentage : null,
                      total: rule.wednesday_price !== null && rule.wednesday_price !== undefined ? rule.wednesday_price / 100 : null,
                    },
                    thu: {
                      gross_cost: rule.thursday_gross_cost !== null && rule.thursday_gross_cost !== undefined ? rule.thursday_gross_cost / 100 : null,
                      fixed_margin: rule.thursday_fixed_margin !== null && rule.thursday_fixed_margin !== undefined ? rule.thursday_fixed_margin / 100 : null,
                      margin_percentage: rule.thursday_margin_percentage !== null && rule.thursday_margin_percentage !== undefined ? rule.thursday_margin_percentage : null,
                      total: rule.thursday_price !== null && rule.thursday_price !== undefined ? rule.thursday_price / 100 : null,
                    },
                    fri: {
                      gross_cost: rule.friday_gross_cost !== null && rule.friday_gross_cost !== undefined ? rule.friday_gross_cost / 100 : null,
                      fixed_margin: rule.friday_fixed_margin !== null && rule.friday_fixed_margin !== undefined ? rule.friday_fixed_margin / 100 : null,
                      margin_percentage: rule.friday_margin_percentage !== null && rule.friday_margin_percentage !== undefined ? rule.friday_margin_percentage : null,
                      total: rule.friday_price !== null && rule.friday_price !== undefined ? rule.friday_price / 100 : null,
                    },
                    sat: {
                      gross_cost: rule.saturday_gross_cost !== null && rule.saturday_gross_cost !== undefined ? rule.saturday_gross_cost / 100 : null,
                      fixed_margin: rule.saturday_fixed_margin !== null && rule.saturday_fixed_margin !== undefined ? rule.saturday_fixed_margin / 100 : null,
                      margin_percentage: rule.saturday_margin_percentage !== null && rule.saturday_margin_percentage !== undefined ? rule.saturday_margin_percentage : null,
                      total: rule.saturday_price !== null && rule.saturday_price !== undefined ? rule.saturday_price / 100 : null,
                    },
                    sun: {
                      gross_cost: rule.sunday_gross_cost !== null && rule.sunday_gross_cost !== undefined ? rule.sunday_gross_cost / 100 : null,
                      fixed_margin: rule.sunday_fixed_margin !== null && rule.sunday_fixed_margin !== undefined ? rule.sunday_fixed_margin / 100 : null,
                      margin_percentage: rule.sunday_margin_percentage !== null && rule.sunday_margin_percentage !== undefined ? rule.sunday_margin_percentage : null,
                      total: rule.sunday_price !== null && rule.sunday_price !== undefined ? rule.sunday_price / 100 : null,
                    },
                  },
                  currency_code: rule.currency_code,
                  created_at: rule.created_at,
                  updated_at: rule.updated_at,
                };
              }
            })
          );

          // Get seasonal pricing data
          const seasonalPrices = [];
          const seasonalGroups = new Map();

          // For each base price rule, get its seasonal overrides
          for (const basePriceRule of basePriceRules) {
            const seasonalOverrides =
              await hotelPricingService.listSeasonalPriceRules({
                base_price_rule_id: basePriceRule.id,
              });

            // Group seasonal overrides by name, start_date, end_date, and currency
            for (const override of seasonalOverrides) {
              const overrideCurrency =
                override.currency_code || basePriceRule.currency_code || "USD";
              const key = `${override.description || "Unnamed"}_${
                override.start_date
              }_${override.end_date}_${overrideCurrency}`;

              if (!seasonalGroups.has(key)) {
                seasonalGroups.set(key, {
                  id: override.id,
                  name: override.description || "Unnamed",
                  start_date: override.start_date,
                  end_date: override.end_date,
                  currency_code: overrideCurrency,
                  weekday_rules: [],
                });
              }

              // Get the occupancy type and meal plan from the base price rule
              const occupancyTypeId = basePriceRule.occupancy_type_id;
              const mealPlanId = basePriceRule.meal_plan_id;

              // Create weekday prices based on the override amount
              const metadata = override.metadata as any;
              const weekdayPrices = {
                mon: metadata?.weekday_prices?.mon || override.amount,
                tue: metadata?.weekday_prices?.tue || override.amount,
                wed: metadata?.weekday_prices?.wed || override.amount,
                thu: metadata?.weekday_prices?.thu || override.amount,
                fri: metadata?.weekday_prices?.fri || override.amount,
                sat: metadata?.weekday_prices?.sat || override.amount,
                sun: metadata?.weekday_prices?.sun || override.amount,
              };

              // Add this rule to the group
              seasonalGroups.get(key).weekday_rules.push({
                id: override.id,
                occupancy_type_id: occupancyTypeId,
                meal_plan_id: mealPlanId,
                weekday_prices: weekdayPrices,
                currency_code: overrideCurrency,
                priority: override.priority,
              });
            }
          }

          // Convert the map to an array
          seasonalGroups.forEach((group) => {
            seasonalPrices.push(group);
          });

          return {
            room_config_id: roomConfig.id,
            room_config: roomConfig,
            weekday_rules: weekdayRules,
            seasonal_prices: seasonalPrices,
          };
        } catch (error) {
          console.error(
            `Error fetching pricing for room ${roomConfig.id}:`,
            error
          );
          return {
            room_config_id: roomConfig.id,
            room_config: roomConfig,
            weekday_rules: [],
            seasonal_prices: [],
            error: error instanceof Error ? error.message : String(error),
          };
        }
      })
    );

    res.json({
      hotel: hotel[0],
      room_configs: roomConfigs,
      occupancy_configs: occupancyConfigs,
      meal_plans: mealPlans,
      room_pricing_data: roomPricingData,
    });
  } catch (error) {
    console.error("Error fetching comprehensive hotel pricing data:", error);
    res.status(500).json({
      message:
        error instanceof Error
          ? error.message
          : "Failed to retrieve hotel pricing data",
    });
  }
};
