import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../modules/hotel-management/hotel-pricing";
import { Modules } from "@camped-ai/framework/utils";
import { CurrencyValidationService } from "../../../../../../modules/hotel-management/hotel-pricing/services/currency-validation";

// Validation schema for cost and margin data (reused from bulk endpoint)
const CostMarginSchema = z.object({
  gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
  fixed_margin: z.number().nullable().optional(),
  margin_percentage: z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%").nullable().optional(),
});

// Validation schema for weekday pricing (enhanced with cost/margin support)
export const PostAdminWeekdayPricing = z.object({
  occupancy_type_id: z.string(),
  meal_plan_id: z.string().optional(),
  // Legacy weekday prices (for backward compatibility)
  weekday_prices: z.object({
    mon: z.number().min(0, "Price must be non-negative"),
    tue: z.number().min(0, "Price must be non-negative"),
    wed: z.number().min(0, "Price must be non-negative"),
    thu: z.number().min(0, "Price must be non-negative"),
    fri: z.number().min(0, "Price must be non-negative"),
    sat: z.number().min(0, "Price must be non-negative"),
    sun: z.number().min(0, "Price must be non-negative"),
  }).optional(),
  // Default cost and margin values
  default_values: z.object({
    gross_cost: z.number().min(0, "Default gross cost must be non-negative").nullable().optional(),
    fixed_margin: z.number().nullable().optional(),
    margin_percentage: z.number().min(0, "Default margin percentage must be non-negative").max(1000, "Default margin percentage cannot exceed 1000%").nullable().optional(),
  }).optional(),
  // Day-specific cost and margin data
  cost_margin_data: z.object({
    mon: CostMarginSchema.optional(),
    tue: CostMarginSchema.optional(),
    wed: CostMarginSchema.optional(),
    thu: CostMarginSchema.optional(),
    fri: CostMarginSchema.optional(),
    sat: CostMarginSchema.optional(),
    sun: CostMarginSchema.optional(),
  }).optional(),
  currency_code: z.string().optional().refine(
    (code) => !code || CurrencyValidationService.isValidCurrencyFormat(code),
    { message: "Currency code must be a valid 3-letter ISO 4217 code" }
  ),
}).refine(
  (data) => data.weekday_prices || data.cost_margin_data,
  { message: "Either weekday_prices or cost_margin_data must be provided" }
);

export type PostAdminWeekdayPricingType = z.infer<typeof PostAdminWeekdayPricing>;

/**
 * GET /admin/hotel-management/room-configs/:id/weekday-pricing
 *
 * Get weekday pricing rules for a room configuration
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;

    if (!roomConfigId) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    console.log(`Fetching weekday pricing for room config: ${roomConfigId}`);

   // Get the hotel pricing service for occupancy and meal plan details
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    // Get all base price rules for this room configuration
    const basePriceRules = await hotelPricingService.listBasePriceRules({
      room_config_id: roomConfigId,
    });

    // Format the response to include weekday_prices and cost/margin data
    const formattedRules = basePriceRules.map(rule => ({
      id: rule.id,
      occupancy_type_id: rule.occupancy_type_id,
      meal_plan_id: rule.meal_plan_id,
      amount: rule.amount,
      room_config_id: rule.room_config_id,
      weekday_prices: {
        mon: rule.monday_price ? rule.monday_price / 100 : (rule.amount ? rule.amount / 100 : null),
        tue: rule.tuesday_price ? rule.tuesday_price / 100 : (rule.amount ? rule.amount / 100 : null),
        wed: rule.wednesday_price ? rule.wednesday_price / 100 : (rule.amount ? rule.amount / 100 : null),
        thu: rule.thursday_price ? rule.thursday_price / 100 : (rule.amount ? rule.amount / 100 : null),
        fri: rule.friday_price ? rule.friday_price / 100 : (rule.amount ? rule.amount / 100 : null),
        sat: rule.saturday_price ? rule.saturday_price / 100 : (rule.amount ? rule.amount / 100 : null),
        sun: rule.sunday_price ? rule.sunday_price / 100 : (rule.amount ? rule.amount / 100 : null),
      },
      // Default cost and margin values (convert from cents to display units)
      default_values: {
        gross_cost: rule.default_gross_cost ? rule.default_gross_cost / 100 : null,
        fixed_margin: rule.default_fixed_margin ? rule.default_fixed_margin / 100 : null,
        margin_percentage: rule.default_margin_percentage,
        total: rule.default_total ? rule.default_total / 100 : null,
      },
      // Day-specific cost and margin data (legacy format)
      cost_margin_data: {
        mon: {
          gross_cost: rule.monday_gross_cost !== null && rule.monday_gross_cost !== undefined ? rule.monday_gross_cost / 100 : null,
          fixed_margin: rule.monday_fixed_margin !== null && rule.monday_fixed_margin !== undefined ? rule.monday_fixed_margin / 100 : null,
          margin_percentage: rule.monday_margin_percentage !== null && rule.monday_margin_percentage !== undefined ? rule.monday_margin_percentage : null,
        },
        tue: {
          gross_cost: rule.tuesday_gross_cost !== null && rule.tuesday_gross_cost !== undefined ? rule.tuesday_gross_cost / 100 : null,
          fixed_margin: rule.tuesday_fixed_margin !== null && rule.tuesday_fixed_margin !== undefined ? rule.tuesday_fixed_margin / 100 : null,
          margin_percentage: rule.tuesday_margin_percentage !== null && rule.tuesday_margin_percentage !== undefined ? rule.tuesday_margin_percentage : null,
        },
        wed: {
          gross_cost: rule.wednesday_gross_cost !== null && rule.wednesday_gross_cost !== undefined ? rule.wednesday_gross_cost / 100 : null,
          fixed_margin: rule.wednesday_fixed_margin !== null && rule.wednesday_fixed_margin !== undefined ? rule.wednesday_fixed_margin / 100 : null,
          margin_percentage: rule.wednesday_margin_percentage !== null && rule.wednesday_margin_percentage !== undefined ? rule.wednesday_margin_percentage : null,
        },
        thu: {
          gross_cost: rule.thursday_gross_cost !== null && rule.thursday_gross_cost !== undefined ? rule.thursday_gross_cost / 100 : null,
          fixed_margin: rule.thursday_fixed_margin !== null && rule.thursday_fixed_margin !== undefined ? rule.thursday_fixed_margin / 100 : null,
          margin_percentage: rule.thursday_margin_percentage !== null && rule.thursday_margin_percentage !== undefined ? rule.thursday_margin_percentage : null,
        },
        fri: {
          gross_cost: rule.friday_gross_cost !== null && rule.friday_gross_cost !== undefined ? rule.friday_gross_cost / 100 : null,
          fixed_margin: rule.friday_fixed_margin !== null && rule.friday_fixed_margin !== undefined ? rule.friday_fixed_margin / 100 : null,
          margin_percentage: rule.friday_margin_percentage !== null && rule.friday_margin_percentage !== undefined ? rule.friday_margin_percentage : null,
        },
        sat: {
          gross_cost: rule.saturday_gross_cost !== null && rule.saturday_gross_cost !== undefined ? rule.saturday_gross_cost / 100 : null,
          fixed_margin: rule.saturday_fixed_margin !== null && rule.saturday_fixed_margin !== undefined ? rule.saturday_fixed_margin / 100 : null,
          margin_percentage: rule.saturday_margin_percentage !== null && rule.saturday_margin_percentage !== undefined ? rule.saturday_margin_percentage : null,
        },
        sun: {
          gross_cost: rule.sunday_gross_cost !== null && rule.sunday_gross_cost !== undefined ? rule.sunday_gross_cost / 100 : null,
          fixed_margin: rule.sunday_fixed_margin !== null && rule.sunday_fixed_margin !== undefined ? rule.sunday_fixed_margin / 100 : null,
          margin_percentage: rule.sunday_margin_percentage !== null && rule.sunday_margin_percentage !== undefined ? rule.sunday_margin_percentage : null,
        },
      },
      // New weekday_values structure (includes total for each day)
      weekday_values: {
        mon: {
          gross_cost: rule.monday_gross_cost !== null && rule.monday_gross_cost !== undefined ? rule.monday_gross_cost / 100 : null,
          fixed_margin: rule.monday_fixed_margin !== null && rule.monday_fixed_margin !== undefined ? rule.monday_fixed_margin / 100 : null,
          margin_percentage: rule.monday_margin_percentage !== null && rule.monday_margin_percentage !== undefined ? rule.monday_margin_percentage : null,
          total: rule.monday_price !== null && rule.monday_price !== undefined ? rule.monday_price / 100 : null,
        },
        tue: {
          gross_cost: rule.tuesday_gross_cost !== null && rule.tuesday_gross_cost !== undefined ? rule.tuesday_gross_cost / 100 : null,
          fixed_margin: rule.tuesday_fixed_margin !== null && rule.tuesday_fixed_margin !== undefined ? rule.tuesday_fixed_margin / 100 : null,
          margin_percentage: rule.tuesday_margin_percentage !== null && rule.tuesday_margin_percentage !== undefined ? rule.tuesday_margin_percentage : null,
          total: rule.tuesday_price !== null && rule.tuesday_price !== undefined ? rule.tuesday_price / 100 : null,
        },
        wed: {
          gross_cost: rule.wednesday_gross_cost !== null && rule.wednesday_gross_cost !== undefined ? rule.wednesday_gross_cost / 100 : null,
          fixed_margin: rule.wednesday_fixed_margin !== null && rule.wednesday_fixed_margin !== undefined ? rule.wednesday_fixed_margin / 100 : null,
          margin_percentage: rule.wednesday_margin_percentage !== null && rule.wednesday_margin_percentage !== undefined ? rule.wednesday_margin_percentage : null,
          total: rule.wednesday_price !== null && rule.wednesday_price !== undefined ? rule.wednesday_price / 100 : null,
        },
        thu: {
          gross_cost: rule.thursday_gross_cost !== null && rule.thursday_gross_cost !== undefined ? rule.thursday_gross_cost / 100 : null,
          fixed_margin: rule.thursday_fixed_margin !== null && rule.thursday_fixed_margin !== undefined ? rule.thursday_fixed_margin / 100 : null,
          margin_percentage: rule.thursday_margin_percentage !== null && rule.thursday_margin_percentage !== undefined ? rule.thursday_margin_percentage : null,
          total: rule.thursday_price !== null && rule.thursday_price !== undefined ? rule.thursday_price / 100 : null,
        },
        fri: {
          gross_cost: rule.friday_gross_cost !== null && rule.friday_gross_cost !== undefined ? rule.friday_gross_cost / 100 : null,
          fixed_margin: rule.friday_fixed_margin !== null && rule.friday_fixed_margin !== undefined ? rule.friday_fixed_margin / 100 : null,
          margin_percentage: rule.friday_margin_percentage !== null && rule.friday_margin_percentage !== undefined ? rule.friday_margin_percentage : null,
          total: rule.friday_price !== null && rule.friday_price !== undefined ? rule.friday_price / 100 : null,
        },
        sat: {
          gross_cost: rule.saturday_gross_cost !== null && rule.saturday_gross_cost !== undefined ? rule.saturday_gross_cost / 100 : null,
          fixed_margin: rule.saturday_fixed_margin !== null && rule.saturday_fixed_margin !== undefined ? rule.saturday_fixed_margin / 100 : null,
          margin_percentage: rule.saturday_margin_percentage !== null && rule.saturday_margin_percentage !== undefined ? rule.saturday_margin_percentage : null,
          total: rule.saturday_price !== null && rule.saturday_price !== undefined ? rule.saturday_price / 100 : null,
        },
        sun: {
          gross_cost: rule.sunday_gross_cost !== null && rule.sunday_gross_cost !== undefined ? rule.sunday_gross_cost / 100 : null,
          fixed_margin: rule.sunday_fixed_margin !== null && rule.sunday_fixed_margin !== undefined ? rule.sunday_fixed_margin / 100 : null,
          margin_percentage: rule.sunday_margin_percentage !== null && rule.sunday_margin_percentage !== undefined ? rule.sunday_margin_percentage : null,
          total: rule.sunday_price !== null && rule.sunday_price !== undefined ? rule.sunday_price / 100 : null,
        },
      },
      currency_code: rule.currency_code,
      created_at: rule.created_at,
      updated_at: rule.updated_at,
    }));

   

    // Get occupancy types and meal plans for each rule
    const rulesWithDetails = await Promise.all(formattedRules.map(async (rule) => {
      try {
        // Get the occupancy type
        const occupancyConfig = await hotelPricingService.retrieveOccupancyConfig(rule.occupancy_type_id);

        // Get the meal plan if it exists
        let mealPlan = null;
        if (rule.meal_plan_id) {
          mealPlan = await hotelPricingService.retrieveMealPlan(rule.meal_plan_id);
        }

        return {
          ...rule,
          occupancy_type: occupancyConfig,
          meal_plan: mealPlan,
        };
      } catch (error) {
        console.error(`Error fetching details for rule ${rule.id}:`, error);
        return rule;
      }
    }));

    res.json({
      weekday_rules: rulesWithDetails,
    });
  } catch (error) {
    console.error("Error fetching weekday pricing:", error);
    res.status(500).json({
      message: "An error occurred while fetching weekday pricing",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};


